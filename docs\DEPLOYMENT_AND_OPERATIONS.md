# Deployment and Operations Guide

## Overview

This guide covers the deployment, configuration, monitoring, and operational aspects of the CareMate Agent System. The system is designed for production environments with high availability, scalability, and reliability requirements.

## System Requirements

### Hardware Requirements

#### Minimum Requirements
- **CPU**: 2 cores, 2.4 GHz
- **RAM**: 4 GB
- **Storage**: 50 GB SSD
- **Network**: 100 Mbps

#### Recommended Requirements
- **CPU**: 4+ cores, 3.0 GHz
- **RAM**: 8+ GB
- **Storage**: 100+ GB SSD with backup
- **Network**: 1 Gbps

#### Production Requirements
- **CPU**: 8+ cores, 3.2 GHz
- **RAM**: 16+ GB
- **Storage**: 500+ GB SSD with RAID
- **Network**: 10 Gbps with redundancy

### Software Requirements

#### Core Dependencies
- **Node.js**: v18.0.0 or higher
- **npm**: v8.0.0 or higher
- **PostgreSQL**: v13.0 or higher
- **RabbitMQ**: v3.8.0 or higher

#### Optional Dependencies
- **Redis**: For caching (recommended)
- **PM2**: For process management
- **Nginx**: For load balancing
- **Docker**: For containerization

## Installation and Setup

### 1. Environment Setup

#### Clone Repository
```bash
git clone https://git.onetalkhub.com/care/care-agent.git
cd care-agent
```

#### Install Dependencies
```bash
npm install
```

#### Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

#### Required Environment Variables
```bash
# Database Configuration
NODE_ENV=production
DB_URL=postgresql://username:password@localhost:5432/caremate_agent

# RabbitMQ Configuration
RABBITMQ_URL=amqp://username:password@localhost:5672
RABBITMQ_EXCHANGE=caremate_exchange

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_32_character_encryption_key

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/caremate-agent

# Performance
MAX_BATCH_SIZE=1000
COLLECTION_TIMEOUT=5000
SHUTDOWN_TIMEOUT=30000
```

### 2. Database Setup

#### Initialize Database
```bash
# Create database schema
npm run db

# Run migrations
npx sequelize-cli db:migrate

# Seed initial data
npx sequelize-cli db:seed:all
```

#### Verify Database Setup
```bash
# Check agent configurations
npx sequelize-cli db:seed --seed 20250210061800-agent-seeder.js

# Verify tables
psql -d caremate_agent -c "\dt"
```

### 3. RabbitMQ Setup

#### Install RabbitMQ
```bash
# Ubuntu/Debian
sudo apt-get install rabbitmq-server

# CentOS/RHEL
sudo yum install rabbitmq-server

# Start service
sudo systemctl start rabbitmq-server
sudo systemctl enable rabbitmq-server
```

#### Configure RabbitMQ
```bash
# Enable management plugin
sudo rabbitmq-plugins enable rabbitmq_management

# Create user
sudo rabbitmqctl add_user caremate_agent secure_password
sudo rabbitmqctl set_user_tags caremate_agent administrator
sudo rabbitmqctl set_permissions -p / caremate_agent ".*" ".*" ".*"
```

#### Verify RabbitMQ Setup
```bash
# Check status
sudo rabbitmqctl status

# Access management UI
# http://localhost:15672 (guest/guest or caremate_agent/secure_password)
```

## Deployment Strategies

### 1. Single Server Deployment

#### Direct Node.js Execution
```bash
# Run specific agent
node index.js --agent local_connection_batch_100

# Run with automation script
node tests/agent.js --agent local_connection_batch_100
```

#### PM2 Process Management
```bash
# Install PM2 globally
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'inbound-agent-local',
      script: 'index.js',
      args: '--agent local_connection_batch_100',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production'
      }
    },
    {
      name: 'outbound-agent-api1',
      script: 'index.js',
      args: '--agent api_1_outbound',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production'
      }
    }
  ]
};
EOF

# Start agents
pm2 start ecosystem.config.js

# Monitor
pm2 monit

# Save configuration
pm2 save
pm2 startup
```

### 2. Docker Deployment

#### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodeuser -u 1001

# Set permissions
RUN chown -R nodeuser:nodejs /app
USER nodeuser

# Expose port (if needed)
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# Start command
CMD ["node", "index.js"]
```

#### Docker Compose
```yaml
version: '3.8'

services:
  agent-inbound:
    build: .
    environment:
      - NODE_ENV=production
      - DB_URL=**************************************/caremate_agent
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
    command: ["node", "index.js", "--agent", "local_connection_batch_100"]
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    depends_on:
      - db
      - rabbitmq
    restart: unless-stopped

  agent-outbound-api:
    build: .
    environment:
      - NODE_ENV=production
      - DB_URL=**************************************/caremate_agent
      - RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672
    command: ["node", "index.js", "--agent", "api_1_outbound"]
    depends_on:
      - db
      - rabbitmq
    restart: unless-stopped

  db:
    image: postgres:13
    environment:
      - POSTGRES_DB=caremate_agent
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  rabbitmq:
    image: rabbitmq:3-management
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

volumes:
  postgres_data:
  rabbitmq_data:
```

### 3. Kubernetes Deployment

#### Deployment Manifest
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: caremate-agent-inbound
  labels:
    app: caremate-agent
    type: inbound
spec:
  replicas: 2
  selector:
    matchLabels:
      app: caremate-agent
      type: inbound
  template:
    metadata:
      labels:
        app: caremate-agent
        type: inbound
    spec:
      containers:
      - name: agent
        image: caremate/agent:latest
        args: ["--agent", "local_connection_batch_100"]
        env:
        - name: NODE_ENV
          value: "production"
        - name: DB_URL
          valueFrom:
            secretKeyRef:
              name: caremate-secrets
              key: database-url
        - name: RABBITMQ_URL
          valueFrom:
            secretKeyRef:
              name: caremate-secrets
              key: rabbitmq-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
        - name: logs-volume
          mountPath: /app/logs
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: caremate-data-pvc
      - name: logs-volume
        persistentVolumeClaim:
          claimName: caremate-logs-pvc
```

## Monitoring and Observability

### 1. Application Monitoring

#### Health Checks
```javascript
// healthcheck.js
const { sequelize } = require('./models');
const connectRabbitmq = require('./config/rabbitmq');

async function healthCheck() {
  try {
    // Database health
    await sequelize.authenticate();
    console.log('Database: OK');

    // RabbitMQ health
    const rabbit = await connectRabbitmq();
    if (rabbit && rabbit.connection) {
      await rabbit.connection.close();
      console.log('RabbitMQ: OK');
    }

    console.log('Health check: PASSED');
    process.exit(0);
  } catch (error) {
    console.error('Health check: FAILED', error.message);
    process.exit(1);
  }
}

healthCheck();
```

#### Performance Metrics
```javascript
// Performance monitoring integration
const PerformanceMonitor = require('./services/performance.service');

// Custom metrics
const performanceMonitor = new PerformanceMonitor('Agent Performance');

// Track processing metrics
performanceMonitor.startStep('File Processing');
// ... processing logic
performanceMonitor.endStep('File Processing', {
  recordsProcessed: 1000,
  processingTime: 5000,
  errorCount: 2
});

// Generate reports
const metrics = performanceMonitor.complete({
  status: 'success',
  totalRecords: 1000
});
```

### 2. Logging Strategy

#### Log Configuration
```javascript
// config/logger.js
const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'caremate-agent' },
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    new DailyRotateFile({
      filename: 'logs/application-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxSize: '20m',
      maxFiles: '14d'
    }),
    new DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxSize: '20m',
      maxFiles: '30d'
    })
  ]
});

module.exports = logger;
```

#### Structured Logging
```javascript
// Example usage
logger.info('Agent started', {
  agentName: 'local_connection_batch_100',
  agentType: 'Inbound',
  batchSize: 100,
  timestamp: new Date().toISOString()
});

logger.error('Processing failed', {
  agentName: 'api_1_outbound',
  error: error.message,
  stack: error.stack,
  context: { recordId: '123', attempt: 3 }
});
```

### 3. External Monitoring Integration

#### Prometheus Metrics
```javascript
// metrics.js
const client = require('prom-client');

// Create metrics
const processedRecords = new client.Counter({
  name: 'caremate_agent_processed_records_total',
  help: 'Total number of processed records',
  labelNames: ['agent_name', 'agent_type', 'status']
});

const processingDuration = new client.Histogram({
  name: 'caremate_agent_processing_duration_seconds',
  help: 'Processing duration in seconds',
  labelNames: ['agent_name', 'operation']
});

// Usage
processedRecords.inc({ 
  agent_name: 'local_connection_batch_100', 
  agent_type: 'inbound', 
  status: 'success' 
}, 100);

const endTimer = processingDuration.startTimer({ 
  agent_name: 'api_1_outbound', 
  operation: 'api_call' 
});
// ... processing
endTimer();
```

## Security Considerations

### 1. Data Protection

#### Encryption at Rest
- Database encryption for sensitive fields
- File system encryption for data directories
- Backup encryption

#### Encryption in Transit
- TLS/SSL for all network communications
- VPN for inter-service communication
- Certificate management

### 2. Access Control

#### Authentication
- Service account management
- API key rotation
- Multi-factor authentication for admin access

#### Authorization
- Role-based access control (RBAC)
- Principle of least privilege
- Regular access reviews

### 3. Network Security

#### Firewall Configuration
```bash
# Allow only necessary ports
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 5432/tcp  # PostgreSQL (internal only)
sudo ufw allow 5672/tcp  # RabbitMQ (internal only)
sudo ufw deny 15672/tcp  # RabbitMQ Management (admin only)
```

#### Network Segmentation
- Separate networks for different environments
- DMZ for external-facing services
- Internal network for database and message queue

## Backup and Recovery

### 1. Database Backup

#### Automated Backup Script
```bash
#!/bin/bash
# backup-database.sh

DB_NAME="caremate_agent"
DB_USER="postgres"
BACKUP_DIR="/backup/database"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup
pg_dump -U $DB_USER -h localhost $DB_NAME | gzip > $BACKUP_DIR/backup_$DATE.sql.gz

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +30 -delete

# Verify backup
if [ $? -eq 0 ]; then
    echo "Backup completed successfully: backup_$DATE.sql.gz"
else
    echo "Backup failed!"
    exit 1
fi
```

#### Cron Schedule
```bash
# Add to crontab
0 2 * * * /path/to/backup-database.sh
```

### 2. Configuration Backup

#### Agent Configuration Export
```bash
# Export agent configurations
npx sequelize-cli db:seed:undo --seed 20250210061800-agent-seeder.js
npx sequelize-cli db:seed --seed 20250210061800-agent-seeder.js > agent-config-backup.sql
```

### 3. Disaster Recovery

#### Recovery Procedures
1. **Database Recovery**:
   ```bash
   # Restore from backup
   gunzip -c backup_20240101_020000.sql.gz | psql -U postgres caremate_agent
   ```

2. **Configuration Recovery**:
   ```bash
   # Restore agent configurations
   psql -U postgres caremate_agent < agent-config-backup.sql
   ```

3. **Service Recovery**:
   ```bash
   # Restart services
   pm2 restart all
   # or
   kubectl rollout restart deployment/caremate-agent-inbound
   ```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database status
sudo systemctl status postgresql

# Check connections
psql -U postgres -c "SELECT * FROM pg_stat_activity;"

# Test connection
psql -U postgres -d caremate_agent -c "SELECT 1;"
```

#### 2. RabbitMQ Issues
```bash
# Check RabbitMQ status
sudo rabbitmqctl status

# Check queues
sudo rabbitmqctl list_queues

# Purge queue if needed
sudo rabbitmqctl purge_queue hr_csv_data
```

#### 3. Agent Processing Issues
```bash
# Check agent logs
tail -f logs/application-$(date +%Y-%m-%d).log

# Check performance reports
ls -la performances/

# Test agent configuration
node index.js --agent local_connection_batch_100 --dry-run
```

### Performance Tuning

#### Database Optimization
```sql
-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM identity WHERE email = '<EMAIL>';

-- Create indexes
CREATE INDEX idx_identity_email ON identity(email);
CREATE INDEX idx_staging_data_agent_id ON staging_data(agent_id);
```

#### Memory Optimization
```javascript
// Increase Node.js memory limit
node --max-old-space-size=4096 index.js --agent local_connection_batch_100
```

#### Batch Size Tuning
```javascript
// Adjust batch sizes based on system capacity
const optimalBatchSize = Math.min(
  agent.batch_size,
  Math.floor(availableMemory / averageRecordSize)
);
```
