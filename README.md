# CareMate Agent System - Complete Documentation

## Overview

The CareMate Agent System is a comprehensive, dynamic data integration platform designed to facilitate seamless bidirectional data flow between the CareMate healthcare management system and external sources. Built with Node.js and leveraging RabbitMQ for message queuing, the system provides robust, scalable, and secure data processing capabilities.

## System Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           CAREMATE AGENT SYSTEM                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐             │
│  │   INBOUND       │    │   RABBITMQ      │    │   OUTBOUND      │             │
│  │   AGENTS        │    │   MESSAGE       │    │   AGENTS        │             │
│  │                 │    │   BROKER        │    │                 │             │
│  │ ┌─────────────┐ │    │                 │    │ ┌─────────────┐ │             │
│  │ │ FTP/SFTP    │ │    │ ┌─────────────┐ │    │ │ CSV Export  │ │             │
│  │ │ Sources     │ │────┤ │hr_csv_data  │ │    │ │ Handler     │ │             │
│  │ └─────────────┘ │    │ │   Queue     │ │    │ └─────────────┘ │             │
│  │                 │    │ └─────────────┘ │    │                 │             │
│  │ ┌─────────────┐ │    │                 │    │ ┌─────────────┐ │             │
│  │ │ AWS S3      │ │    │ ┌─────────────┐ │    │ │ API         │ │             │
│  │ │ Sources     │ │────┤ │api_1_out    │ │────┤ │ Outbound    │ │             │
│  │ └─────────────┘ │    │ │bound_queue  │ │    │ │ Handler     │ │             │
│  │                 │    │ └─────────────┘ │    │ └─────────────┘ │             │
│  │ ┌─────────────┐ │    │                 │    │                 │             │
│  │ │ Azure Blob  │ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │             │
│  │ │ Sources     │ │────┤ │api_2_out    │ │────┤ │ XML         │ │             │
│  │ └─────────────┘ │    │ │bound_queue  │ │    │ │ Generator   │ │             │
│  │                 │    │ └─────────────┘ │    │ │ Handler     │ │             │
│  │ ┌─────────────┐ │    │                 │    │ └─────────────┘ │             │
│  │ │ Local       │ │    │ ┌─────────────┐ │    │                 │             │
│  │ │ Directory   │ │────┤ │csv_gen_out  │ │────┤                 │             │
│  │ └─────────────┘ │    │ │bound_queue  │ │    │                 │             │
│  │                 │    │ └─────────────┘ │    │                 │             │
│  │ ┌─────────────┐ │    │                 │    │                 │             │
│  │ │ URL/API     │ │    │ ┌─────────────┐ │    │                 │             │
│  │ │ Sources     │ │────┤ │ccure9000_xml│ │────┤                 │             │
│  │ └─────────────┘ │    │ │_out_queue   │ │    │                 │             │
│  └─────────────────┘    │ └─────────────┘ │    │                 │             │
│                         └─────────────────┘    └─────────────────┘             │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                              PROCESSOR SYSTEM                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐             │
│  │   STAGING       │    │   RULE ENGINE   │    │   DATABASE      │             │
│  │   DATA          │    │                 │    │                 │             │
│  │                 │    │ ┌─────────────┐ │    │ ┌─────────────┐ │             │
│  │ ┌─────────────┐ │    │ │ Identity    │ │    │ │ Identity    │ │             │
│  │ │ CSV Data    │ │────┤ │ Rules       │ │────┤ │ Table       │ │             │
│  │ │ Processing  │ │    │ └─────────────┘ │    │ └─────────────┘ │             │
│  │ └─────────────┘ │    │                 │    │                 │             │
│  │                 │    │ ┌─────────────┐ │    │ ┌─────────────┐ │             │
│  │ ┌─────────────┐ │    │ │ Event       │ │    │ │ Facility    │ │             │
│  │ │ Data        │ │────┤ │ Generation  │ │────┤ │ Tables      │ │             │
│  │ │ Validation  │ │    │ └─────────────┘ │    │ └─────────────┘ │             │
│  │ └─────────────┘ │    │                 │    │                 │             │
│  │                 │    │ ┌─────────────┐ │    │ ┌─────────────┐ │             │
│  │ ┌─────────────┐ │    │ │ Outbound    │ │    │ │ Access      │ │             │
│  │ │ Mapping     │ │────┤ │ Event       │ │────┤ │ Control     │ │             │
│  │ │ Transform   │ │    │ │ Triggers    │ │    │ │ Tables      │ │             │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │             │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘             │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Project Structure

```
caremate-agent/
├── agents/
│   ├── inbound.agent.js      # Inbound agent handler
│   ├── outbound.agent.js     # Outbound agent handler
│   └── index.js              # Agent loader
├── handlers/
│   ├── sendHrData.handler.js # CSV processing handler
│   ├── generateCsv.handler.js# CSV generation handler
│   ├── sendApiData.handler.js# API outbound handler
│   ├── generateXml.handler.js# XML generation handler
│   └── index.js              # Handler loader
├── mappings/
│   ├── hrData.mapping.json   # Inbound CSV mapping
│   ├── csv_generation_outbound.mapping.json
│   ├── api1Outbound.mapping.json
│   ├── api2Outbound.mapping.json
│   └── ccure9000Outbound.mapping.json
├── models/
│   ├── agent.model.js        # Agent configuration model
│   ├── agentSetting.model.js # Agent settings model
│   ├── identity.model.js     # Identity data model
│   ├── stagingData.model.js  # Staging data model
│   └── index.js              # Model loader
├── seeders/
│   ├── 20250210061800-agent-seeder.js
│   ├── 20250210061810-cron-config-seeder.js
│   └── other-seeders.js
├── config/
│   ├── config.js             # Application configuration
│   ├── logger.js             # Logging configuration
│   ├── rabbitmq.js           # RabbitMQ configuration
│   └── sequelize.js          # Database configuration
├── services/
│   ├── csv.service.js        # CSV processing service
│   ├── event.service.js      # Event/RabbitMQ service
│   ├── performance.service.js# Performance monitoring
│   └── encryption.service.js # Encryption service
├── helpers/
│   ├── caching.helper.js     # Configuration caching
│   ├── agent.helper.js       # Agent utilities
│   └── global.helper.js      # Global utilities
├── tests/
│   ├── agent.js              # Automation script
│   ├── testApi1.js           # Test API server 1
│   └── testApi2.js           # Test API server 2
├── performances/             # Performance reports
├── logs/                     # Application logs
├── .env                      # Environment variables
├── index.js                  # Main entry point
└── package.json              # Dependencies and scripts
```

## Core Components

### 1. Agent Types

#### Inbound Agents
- **Purpose**: Fetch data from external sources and process it into the CareMate system
- **Sources Supported**:
  - FTP/SFTP servers
  - AWS S3 buckets
  - Azure Blob Storage
  - Local directories
  - HTTP/HTTPS URLs
- **Processing Flow**: Source → CSV Processing → Staging → Database
- **Handler**: `sendHrData`

#### Outbound Agents
- **Purpose**: Send data from CareMate system to external destinations
- **Types**:
  - **CSV Outbound**: Generate CSV files from database models
  - **API Outbound**: Send data to external systems via REST APIs
  - **XML Outbound**: Generate XML files for specialized systems (e.g., CCURE9000)
- **Handlers**: `generateCsv`, `sendApiData`, `generateXml`

### 2. Dynamic Configuration System

All agents are configured through database seeders, making the system completely dynamic:

```
Agent Configuration:
├── Agent Table (agent.model.js)
│   ├── name (unique identifier)
│   ├── type (Inbound/Outbound)
│   ├── source (Local/FTP/S3/Azure/URL/API)
│   ├── handler (sendHrData/generateCsv/sendApiData/generateXml)
│   ├── mapping (reference to mapping file)
│   ├── queue (RabbitMQ queue name)
│   ├── schema (database model for outbound)
│   └── status (active/inactive)
└── Agent Settings Table (agentSetting.model.js)
    ├── Encrypted connection details
    ├── API credentials
    ├── Directory paths
    └── Configuration parameters
```

### 3. Message Queue Architecture

The system uses RabbitMQ for asynchronous message processing:

- **Inbound Flow**: External Source → Agent → RabbitMQ → Processor → Database
- **Outbound Flow**: Rule Engine → RabbitMQ → Agent → External Destination
- **Collection Processing**: Messages are batched for optimal performance
- **Error Handling**: Failed messages are requeued with retry logic

### 4. Mapping System

Data transformation is handled through JSON mapping files:

#### Inbound Mappings (CSV to Database)
```json
{
  "Identity.email": "email",
  "Identity.first_name": "firstName",
  "Identity.last_name": "lastName"
}
```

#### Outbound Mappings (Database to External Format)
```json
{
  "mappings": {
    "Identity.identity_id": "Employee_ID",
    "Identity.first_name": "First_Name",
    "Identity.email": "Email_Address"
  }
}
```

#### API Mappings (Database to API Format)
```json
{
  "mappingType": "apiTransform",
  "apiConfig": {
    "method": "POST",
    "headers": {
      "Content-Type": "application/json",
      "X-API-Key": "{{api_key}}"
    }
  },
  "dataTransform": {
    "employee": {
      "id": "{{Identity.eid}}",
      "email": "{{Identity.email}}"
    }
  }
}
```

## Agent Configuration Examples

### Inbound Agent (Local Directory)
```javascript
{
  name: 'local_connection_batch_100',
  display_name: 'HR Local Directory',
  description: 'Local directory for HR files',
  source: 'Local',
  type: 'Inbound',
  handler: 'sendHrData',
  mapping: 'hrData',
  queue: 'hr_csv_data',
  stagging_key: 'email',
  batch_size: 100,
  status: true,
  settings: {
    directory_path: 'E:\\Windows\\Desktop\\care\\csv\\batch100'
  }
}
```

### Outbound Agent (CSV Generation)
```javascript
{
  name: 'local_connection_outbound_batch_100',
  display_name: 'HR Data Outbound Local Directory',
  description: 'Local directory for outbound CSV generation',
  source: 'Local',
  type: 'Outbound',
  handler: 'generateCsv',
  mapping: 'csv_generation_outbound',
  queue: 'csv_generation_outbound_queue',
  schema: 'Identity',
  batch_size: 100,
  settings: {
    directory_path: 'E:\\Windows\\Desktop\\care\\csv\\outbound'
  }
}
```

### Outbound Agent (API with Key Authentication)
```javascript
{
  name: 'api_1_outbound',
  display_name: 'External Partner API Outbound',
  description: 'Send employee data to external partner via API',
  source: 'API',
  type: 'Outbound',
  handler: 'sendApiData',
  mapping: 'api1Outbound',
  queue: 'api_1_outbound_queue',
  schema: 'Identity',
  batch_size: 50,
  settings: {
    api_url: 'http://localhost:3051/log',
    api_key: 'your_api_key_here',
    client_id: 'caremate_client',
    timeout: 45000,
    retries: 3
  }
}
```

### Outbound Agent (API with Token Authentication)
```javascript
{
  name: 'api_2_outbound',
  display_name: 'Government System API Outbound',
  description: 'Send employee data to government system via API with token authentication',
  source: 'API',
  type: 'Outbound',
  handler: 'sendApiData',
  mapping: 'api2Outbound',
  queue: 'api_2_outbound_queue',
  schema: 'Identity',
  batch_size: 25,
  settings: {
    api_url: 'http://localhost:3052/log',
    token_url: 'http://localhost:3052/auth/token',
    client_id: 'caremate_gov_client',
    client_secret: 'super_secret_key_123',
    grant_type: 'client_credentials',
    scope: 'employee_data_write',
    retries: 5
  }
}
```

### Outbound Agent (XML Generation)
```javascript
{
  name: 'ccure9000_xml_outbound_local',
  display_name: 'CCURE9000 XML Local Drop',
  description: 'Generate XML files for CCURE9000 personnel, credentials, and clearances import',
  source: 'Local',
  type: 'Outbound',
  handler: 'generateXml',
  mapping: 'ccure9000Outbound',
  queue: 'ccure9000_xml_outbound_queue',
  schema: 'Identity',
  batch_size: 100,
  settings: {
    directory_path: 'E:\\Windows\\Desktop\\care\\csv\\outbound_xml_drop'
  }
}
```

## Data Mapping Patterns

### Inbound Mapping (CSV to Database)
Simple key-value mappings to transform CSV data to database fields:

```json
{
  "Identity.email": "email",
  "Identity.first_name": "firstName",
  "Identity.last_name": "lastName",
  "Identity.middle_name": "middleName",
  "Identity.eid": "employeeId",
  "Identity.identity_type": "identityType",
  "Identity.national_id": "nationalId",
  "Identity.suffix": "suffix",
  "Identity.mobile": "mobile",
  "Identity.start_date": "startDate",
  "Identity.end_date": "endDate",
  "Identity.status": "status",
  "Identity.company": "company",
  "Identity.organization": "organization",
  "Identity.company_code": "companyCode",
  "Identity.job_title": "jobTitle",
  "Identity.job_code": "jobCode"
}
```

### Positional Mapping (CSV without headers)
For CSV files without headers, use positional mapping:

```json
{
  "Identity.email": "0",
  "Identity.first_name": "1",
  "Identity.last_name": "2",
  "Identity.middle_name": "3",
  "Identity.eid": "4",
  "Identity.identity_type": "5",
  "Identity.national_id": "6",
  "Identity.suffix": "7",
  "Identity.mobile": "8",
  "Identity.start_date": "9",
  "Identity.end_date": "10",
  "Identity.status": "11",
  "Identity.company": "12",
  "Identity.organization": "13",
  "Identity.company_code": "14",
  "Identity.job_title": "15",
  "Identity.job_code": "16"
}
```

### API Transform Mapping
Complex JSON transformation for API endpoints:

```json
{
  "mappingType": "apiTransform",
  "apiConfig": {
    "method": "POST",
    "headers": {
      "Content-Type": "application/json",
      "X-API-Key": "{{api_key}}",
      "X-Client-ID": "{{client_id}}"
    },
    "timeout": 45000,
    "retries": 3,
    "retryDelay": 2000,
    "retryBackoff": "exponential"
  },
  "dataTransform": {
    "type": "object",
    "properties": {
      "employee": {
        "employeeId": "{{Identity.eid}}",
        "personalInfo": {
          "email": "{{Identity.email}}",
          "fullName": "{{Identity.first_name}} {{Identity.middle_name}} {{Identity.last_name}}",
          "firstName": "{{Identity.first_name}}",
          "lastName": "{{Identity.last_name}}",
          "nationalIdentifier": "{{Identity.national_id}}",
          "phoneNumber": "{{Identity.mobile}}"
        },
        "employment": {
          "startDate": "{{Identity.start_date}}",
          "endDate": "{{Identity.end_date}}",
          "status": "{{Identity.status}}",
          "companyName": "{{Identity.company}}",
          "organizationUnit": "{{Identity.organization}}",
          "companyCode": "{{Identity.company_code}}",
          "jobTitle": "{{Identity.job_title}}",
          "jobCode": "{{Identity.job_code}}"
        }
      },
      "batchInfo": {
        "batchId": "{{batch_id}}",
        "timestamp": "{{current_timestamp}}",
        "source": "caremate",
        "totalRecords": "{{batch_size}}"
      }
    }
  },
  "validation": {
    "required": ["employee.employeeId", "employee.personalInfo.email"],
    "rules": {
      "employee.personalInfo.email": {
        "type": "email",
        "required": true
      },
      "employee.personalInfo.firstName": {
        "type": "string",
        "required": true,
        "minLength": 1
      }
    }
  }
}
```

### XML Transform Mapping (CCURE9000)
Structured XML generation for specialized systems:

```json
{
  "mappingType": "xmlTransform",
  "xmlConfig": {
    "rootElement": "CCURE9000Import",
    "xmlDeclaration": {
      "version": "1.0",
      "encoding": "UTF-8"
    }
  },
  "dataTransform": {
    "ImportHeader": {
      "ImportId": "{{batch_id}}",
      "ImportDate": "{{current_timestamp}}",
      "Source": "CareMate_System",
      "Version": "1.0",
      "RecordCount": "{{batch_size}}"
    },
    "Personnel": [
      {
        "PersonnelId": "{{Identity.eid}}",
        "FirstName": "{{Identity.first_name}}",
        "LastName": "{{Identity.last_name}}",
        "MiddleName": "{{Identity.middle_name}}",
        "Email": "{{Identity.email}}",
        "NationalId": "{{Identity.national_id}}",
        "Mobile": "{{Identity.mobile}}",
        "Company": "{{Identity.company}}",
        "Organization": "{{Identity.organization}}",
        "CompanyCode": "{{Identity.company_code}}",
        "JobTitle": "{{Identity.job_title}}",
        "JobCode": "{{Identity.job_code}}",
        "StartDate": "{{Identity.start_date}}",
        "EndDate": "{{Identity.end_date}}",
        "Status": "{{Identity.status}}",
        "CreatedDate": "{{current_timestamp}}",
        "UpdatedDate": "{{current_timestamp}}"
      }
    ],
    "Credentials": [
      {
        "CredentialId": "CARD_{{Identity.eid}}",
        "PersonnelId": "{{Identity.eid}}",
        "CredentialType": "ProximityCard",
        "CardNumber": "{{Identity.eid}}_CARD",
        "FacilityCode": "{{Identity.company_code}}",
        "Status": "Active",
        "IssueDate": "{{Identity.start_date}}",
        "ExpiryDate": "{{Identity.end_date}}",
        "CreatedDate": "{{current_timestamp}}"
      }
    ]
  }
}
```

## Installation and Setup

### Prerequisites
- Node.js v18.0.0 or higher
- PostgreSQL v13.0 or higher
- RabbitMQ v3.8.0 or higher

### Basic Setup
```bash
# Clone repository
git clone -b darshil https://git.onetalkhub.com/care/care-agent.git
mv care-agent agent
cd care-agent

# Install dependencies
npm install
```

## Running Agents

### Command Line Interface

#### Main Entry Point
```bash
# Run specific agent
node index.js --agent <agent_name>

# Examples
node index.js --agent local_connection_batch_100
node index.js --agent api_1_outbound
node index.js --agent ccure9000_xml_outbound_local
```

#### Automation Script (with cleanup)
```bash
# Run with full automation (cleanup + execution)
node tests/agent.js --agent <agent_name>

# Features:
# - Purges RabbitMQ queues
# - Clears database tables (identity, staging_data)
# - Moves files from archive directories
# - Launches main agent process
```

### NPM Scripts
```bash
# Development
npm run dev                 # Run with nodemon
npm run agent              # Run automation script

# Outbound agents
npm run outbound_csv       # CSV generation
npm run outbound_xml       # XML generation
npm run outbound_api_1     # API outbound (key auth)
npm run outbound_api_2     # API outbound (token auth)

# Test API servers
npm run test_api_1         # Test server on port 3051
npm run test_api_2         # Test server on port 3052

# Database operations
npm run db                 # Sync database
npm run db:refresh         # Refresh database
```

## Database Management

### Migration Commands
```bash
# Run migrations
npx sequelize-cli db:migrate

# Undo migrations
npx sequelize-cli db:migrate:undo
npx sequelize-cli db:migrate:undo:all
```

### Seeder Commands
```bash
# Run all seeders
npx sequelize-cli db:seed:all

# Run specific seeder
npx sequelize-cli db:seed --seed 20250210061800-agent-seeder.js

# Undo seeders
npx sequelize-cli db:seed:undo:all
npx sequelize-cli db:seed:undo --seed 20250210061800-agent-seeder.js

# Run with specific environment
npx sequelize-cli db:seed:all --env-file .env.development
```

### Database Refresh
```bash
# Refresh entire database
npm run db:refresh

# Refresh with specific environment
npm run db:refresh -- --env-file .env.dev

# Refresh specific models
npm run db:refresh -- --model StagingData Facility Floor Room
```

## Data Processing Flow

### Inbound Data Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   External      │    │   Inbound       │    │   CSV Parser    │
│   Source        │    │   Agent         │    │                 │
│                 │────┤                 │────┤ • Header detect │
│ • FTP/SFTP      │    │ • File fetch    │    │ • Row parsing   │
│ • S3/Azure      │    │ • Validation    │    │ • Type convert  │
│ • Local/URL     │    │ • Scheduling    │    │ • Error handle  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │   Processor     │    │   Staging       │
│                 │    │                 │    │                 │
│ • Identity      │◄───┤ • Rule engine   │◄───┤ • Mapping apply │
│ • Facility      │    │ • Event trigger │    │ • Deduplication │
│ • Access        │    │ • Validation    │    │ • Change detect │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Outbound Data Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │   Rule Engine   │    │   RabbitMQ      │
│                 │    │                 │    │                 │
│ • Data changes  │────┤ • Event detect  │────┤ • Event queue   │
│ • Triggers      │    │ • Rule eval     │    │ • Message batch │
│ • Schedules     │    │ • Event create  │    │ • Collection    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   External      │    │   Data          │    │   Outbound      │
│   Destination   │    │   Transform     │    │   Agent         │
│                 │    │                 │    │                 │
│ • API endpoint  │◄───┤ • Mapping apply │◄───┤ • Collection    │
│ • File system   │    │ • Format convert│    │ • Processing    │
│ • Cloud storage │    │ • Validation    │    │ • Error handle  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Performance and Monitoring

### Performance Features
- **Collection-based processing** for optimal throughput
- **Configurable batch sizes** (10-1000 records)
- **Asynchronous message processing** with RabbitMQ
- **Connection pooling and caching**
- **Memory-efficient streaming** for large files

### Monitoring Capabilities
- **Real-time performance metrics**
- **Processing time and throughput tracking**
- **Success/failure rate monitoring**
- **API response time analysis**
- **Queue depth and processing delays**
- **Resource utilization tracking**

### Performance Reports
Performance reports are automatically generated and saved to the `/performances/` directory:

```
performances/
├── CSV_Processing_Job_20240121_143022.json
├── API_Outbound_Job_20240121_143155.json
└── XML_Generation_Job_20240121_143301.json
```

Each report contains:
- Processing duration and timestamps
- Record counts and success rates
- Step-by-step performance breakdown
- Error details and recovery actions
- Resource utilization metrics

## Security Features

### Data Protection
- **Encrypted sensitive configuration** in database
- **TLS/SSL for all communications**
- **Secure credential management**
- **Audit trail logging**

### Authentication Patterns
- **API Key Authentication**: Simple header-based auth
- **Token-Based Authentication**: OAuth 2.0 client credentials
- **Token Caching**: Automatic token refresh and caching
- **Multi-factor Authentication**: For admin access

### Access Control
- **Role-based permissions**
- **Service account management**
- **Network security controls**
- **Principle of least privilege**

## Error Handling and Recovery

### Error Types
- **Validation Errors**: Data format and type issues
- **Connection Errors**: Network and service failures
- **Processing Errors**: Mapping and transformation issues
- **API Errors**: External service failures

### Recovery Strategies
- **Exponential Backoff**: Increasing retry delays
- **Circuit Breaker**: Temporary service suspension
- **Dead Letter Queue**: Failed message handling
- **Manual Intervention**: Admin notifications
- **Partial Success**: Process valid records, report failures

### Error Reporting
Comprehensive error tracking with:
- Detailed error context and stack traces
- Failed record identification
- Recovery recommendations
- Performance impact analysis

## Troubleshooting

### Performance Tuning

#### Memory Optimization
```bash
# Increase Node.js memory limit
node --max-old-space-size=4096 index.js --agent local_connection_batch_100
```

#### Batch Size Tuning
- **Small files (< 1000 records)**: batch_size = 50-100
- **Medium files (1000-10000 records)**: batch_size = 100-500
- **Large files (> 10000 records)**: batch_size = 500-1000

## Available Agents (from Seeder)

### Inbound Agents
- `ftp_connection_main` - Main FTP server
- `ftp_connection_backup` - Backup FTP server
- `s3_connection_production` - Production S3 bucket
- `azure_connection_production` - Production Azure storage
- `local_connection_batch_100` - Local directory (batch 100)
- `local_connection_batch_10` - Local directory (batch 10)
- `url_connection_external_api` - External API endpoint
- `url_connection_government_api` - Government data API

### Outbound Agents
- `local_connection_outbound_batch_100` - CSV generation (local)
- `s3_connection_archive` - CSV generation (S3)
- `azure_connection_backup` - CSV generation (Azure)
- `api_1_outbound` - API outbound (key auth)
- `api_2_outbound` - API outbound (token auth)
- `ccure9000_xml_outbound_local` - XML generation (local)
- `ccure9000_xml_outbound_s3` - XML generation (S3)
- `ccure9000_xml_outbound_ftp` - XML generation (FTP)

## Template Variables

### Available Variables
- `{{Identity.field_name}}` - Any field from Identity model
- `{{current_timestamp}}` - Current ISO timestamp
- `{{batch_id}}` - Unique batch identifier (UUID)
- `{{batch_size}}` - Number of records in current batch
- `{{message_id}}` - Unique message identifier
- `{{api_key}}` - Agent's API key setting
- `{{client_id}}` - Agent's client ID setting
- Any other agent setting key

### String Interpolation Examples
```javascript
// Simple field replacement
"{{Identity.first_name}}" → "John"

// Concatenation
"{{Identity.first_name}} {{Identity.last_name}}" → "John Doe"

// Nested object creation
{
  "name": "{{Identity.first_name}} {{Identity.last_name}}",
  "contact": {
    "email": "{{Identity.email}}",
    "phone": "{{Identity.mobile}}"
  }
}
```

## Contributing and Development

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make changes with appropriate tests
4. Submit a pull request with detailed description

### Code Standards
- Follow existing code style and patterns
- Add comprehensive error handling
- Include performance monitoring
- Update documentation for new features

### Testing
```bash
# Start test API servers
npm run test_api_1  # Port 3051
npm run test_api_2  # Port 3052

# Run continuous testing
npm run test_api_2  # Continuous event generation

# Test specific agent
node tests/agent.js --agent local_connection_batch_100
```

---

## License

This project is proprietary software developed by CareMate for internal use and authorized partners.

For detailed technical information, refer to the individual documentation files in the `/docs/` directory.