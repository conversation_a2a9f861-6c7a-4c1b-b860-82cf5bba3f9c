# Inbound Agents Documentation

## Overview

Inbound agents are responsible for fetching data from external sources and processing it into the CareMate system. They support multiple source types and use a standardized CSV processing pipeline.

## Supported Source Types

### 1. FTP/SFTP Sources
- **Source Type**: `FileTransfer`
- **Configuration**:
  ```json
  {
    "host": "ftp.company.com",
    "port": "21",
    "username": "ftp_user",
    "password": "secure_password",
    "path": "/data/hr/incoming"
  }
  ```
- **Features**:
  - Secure file transfer protocols
  - Directory monitoring
  - Automatic file archiving
  - Connection pooling

### 2. AWS S3 Sources
- **Source Type**: `AWSS3`
- **Configuration**:
  ```json
  {
    "access_key_id": "AKIAIOSFODNN7EXAMPLE",
    "secret_access_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLE",
    "bucket_name": "hr-data-bucket",
    "region": "us-east-1"
  }
  ```
- **Features**:
  - Multi-region support
  - IAM role integration
  - Versioning support
  - Lifecycle management

### 3. Azure Blob Storage Sources
- **Source Type**: `AzureBlob`
- **Configuration**:
  ```json
  {
    "connection_string": "DefaultEndpointsProtocol=https;AccountName=...",
    "container_name": "hr-files"
  }
  ```
- **Features**:
  - Managed identity support
  - Hot/Cool/Archive tiers
  - Blob versioning
  - Access policies

### 4. Local Directory Sources
- **Source Type**: `Local`
- **Configuration**:
  ```json
  {
    "directory_path": "E:\\Windows\\Desktop\\care\\csv\\batch100"
  }
  ```
- **Features**:
  - Real-time file monitoring
  - Automatic archiving
  - Duplicate detection
  - Permission management

### 5. URL/HTTP Sources
- **Source Type**: `URL`
- **Configuration**:
  ```json
  {
    "url": "https://api.external-partner.com/data/hr-export.csv"
  }
  ```
- **Features**:
  - HTTP/HTTPS support
  - Authentication headers
  - Retry mechanisms
  - Response validation

## Processing Pipeline

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   File Source   │    │   CSV Parser    │    │   Data Mapper   │
│                 │────┤                 │────┤                 │
│ • FTP/SFTP      │    │ • Header detect │    │ • Field mapping │
│ • S3/Azure      │    │ • Validation    │    │ • Type convert  │
│ • Local/URL     │    │ • Error handle  │    │ • Validation    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │   RabbitMQ      │    │   Staging       │
│                 │    │                 │    │                 │
│ • Identity      │◄───┤ • Batch queue   │◄───┤ • Deduplication │
│ • Facility      │    │ • Error queue   │    │ • Change detect │
│ • Access        │    │ • Retry logic   │    │ • Validation    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Agent Configuration

### Database Schema

#### Agent Table
```sql
CREATE TABLE agent (
    agent_id UUID PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(255),
    description TEXT,
    source VARCHAR(50) NOT NULL,
    type VARCHAR(20) NOT NULL,
    handler VARCHAR(50) NOT NULL,
    mapping VARCHAR(100),
    queue VARCHAR(100),
    stagging_key VARCHAR(50),
    batch_size INTEGER DEFAULT 10,
    status BOOLEAN DEFAULT true,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### Agent Settings Table
```sql
CREATE TABLE agent_setting (
    agent_setting_id UUID PRIMARY KEY,
    agent_id UUID REFERENCES agent(agent_id),
    key VARCHAR(100) NOT NULL,
    value TEXT,
    key_name VARCHAR(255),
    description TEXT,
    is_encrypted BOOLEAN DEFAULT false,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

### Example Agent Configuration

```javascript
{
  name: 'local_connection_batch_100',
  display_name: 'HR Local Directory',
  description: 'Local directory for HR files',
  source: 'Local',
  type: 'Inbound',
  handler: 'sendHrData',
  mapping: 'hrData',
  queue: 'hr_csv_data',
  stagging_key: 'email',
  batch_size: 100,
  status: true,
  settings: {
    directory_path: 'E:\\Windows\\Desktop\\care\\csv\\batch100'
  }
}
```

## Data Mapping

### Mapping File Structure

Inbound agents use simple key-value mappings to transform CSV data to database fields:

```json
{
  "Identity.email": "email",
  "Identity.first_name": "firstName",
  "Identity.last_name": "lastName",
  "Identity.middle_name": "middleName",
  "Identity.eid": "employeeId",
  "Identity.identity_type": "identityType",
  "Identity.national_id": "nationalId",
  "Identity.suffix": "suffix",
  "Identity.mobile": "mobile",
  "Identity.start_date": "startDate",
  "Identity.end_date": "endDate",
  "Identity.status": "status",
  "Identity.company": "company",
  "Identity.organization": "organization",
  "Identity.company_code": "companyCode",
  "Identity.job_title": "jobTitle",
  "Identity.job_code": "jobCode"
}
```

### Positional Mapping

For CSV files without headers, use positional mapping:

```json
{
  "Identity.email": "0",
  "Identity.first_name": "1",
  "Identity.last_name": "2",
  "Identity.middle_name": "3",
  "Identity.eid": "4"
}
```

## Cron Scheduling

Inbound agents support automated scheduling through cron configurations:

### Cron Configuration
```javascript
{
  name: 'inbound_agent',
  display_name: 'Inbound Agent Scheduler',
  description: 'Scheduled processing for inbound agents',
  schedule: '0 */2 * * *', // Every 2 hours
  status: true
}
```

### Supported Cron Patterns
- `0 */2 * * *` - Every 2 hours
- `0 0 * * *` - Daily at midnight
- `0 0 * * 1` - Weekly on Monday
- `0 0 1 * *` - Monthly on 1st day

## Error Handling

### Validation Errors
- **File Format**: Invalid CSV structure
- **Data Type**: Type conversion failures
- **Required Fields**: Missing mandatory data
- **Duplicate Records**: Based on staging key

### Processing Errors
- **Connection Failures**: Source unavailable
- **Permission Errors**: Access denied
- **Queue Errors**: RabbitMQ connection issues
- **Database Errors**: Constraint violations

### Recovery Mechanisms
- **Retry Logic**: Exponential backoff
- **Error Queues**: Failed message handling
- **Manual Intervention**: Admin notifications
- **Data Rollback**: Transaction management

## Performance Optimization

### Batch Processing
- Configurable batch sizes (10-1000 records)
- Memory-efficient streaming
- Parallel processing support
- Progress tracking

### Caching
- Agent configuration caching
- Mapping file caching
- Connection pooling
- Metadata caching

### Monitoring
- Processing time metrics
- Record count tracking
- Error rate monitoring
- Resource utilization

## Running Inbound Agents

### Command Line
```bash
# Run specific inbound agent
node index.js --agent local_connection_batch_100

# Run with automation script (includes cleanup)
node tests/agent.js --agent local_connection_batch_100
```

### Environment Setup
```bash
# Install dependencies
npm install

# Setup database
npm run db

# Seed agent configurations
npx sequelize-cli db:seed --seed 20250210061800-agent-seeder.js
```

### Monitoring
- Check `/performances/` directory for reports
- Monitor RabbitMQ queues
- Review application logs
- Database staging table status
