# API Reference Documentation

## Overview

This document provides comprehensive API reference for the CareMate Agent System, including command-line interfaces, configuration APIs, and integration endpoints.

## Command Line Interface

### Main Entry Point

#### `index.js`
Primary entry point for running individual agents.

```bash
node index.js --agent <agent_name>
```

**Parameters:**
- `--agent <agent_name>` (required): Name of the agent to run

**Examples:**
```bash
# Run inbound agent
node index.js --agent local_connection_batch_100

# Run outbound CSV agent
node index.js --agent local_connection_outbound_batch_100

# Run API outbound agent
node index.js --agent api_1_outbound

# Run XML outbound agent
node index.js --agent ccure9000_xml_outbound_local
```

### Automation Script

#### `tests/agent.js`
Comprehensive automation script with cleanup and agent execution.

```bash
node tests/agent.js --agent <agent_name>
```

**Features:**
- Purges RabbitMQ queues
- Clears database tables (identity, staging_data)
- Moves files from archive directories
- Launches main agent process

**Examples:**
```bash
# Run with full automation
node tests/agent.js --agent local_connection_batch_100
```

### NPM Scripts

#### Package.json Scripts
```bash
# Development with nodemon
npm run dev

# Run automation script
npm run agent

# Outbound agents
npm run outbound_csv
npm run outbound_xml
npm run outbound_api_1
npm run outbound_api_2

# Test API servers
npm run test_api_1  # Port 3051
npm run test_api_2  # Port 3052

# Database operations
npm run db          # Sync database
npm run db:refresh  # Refresh database
```

## Database Management

### Sequelize CLI Commands

#### Migration Commands
```bash
# Run migrations
npx sequelize-cli db:migrate

# Undo last migration
npx sequelize-cli db:migrate:undo

# Undo all migrations
npx sequelize-cli db:migrate:undo:all
```

#### Seeder Commands
```bash
# Run all seeders
npx sequelize-cli db:seed:all

# Run specific seeder
npx sequelize-cli db:seed --seed 20250210061800-agent-seeder.js

# Undo all seeders
npx sequelize-cli db:seed:undo:all

# Undo specific seeder
npx sequelize-cli db:seed:undo --seed 20250210061800-agent-seeder.js

# Run with specific environment
npx sequelize-cli db:seed:all --env-file .env.development
```

#### Database Refresh Commands
```bash
# Refresh entire database
npm run db:refresh

# Refresh with specific environment
npm run db:refresh -- --env-file .env.dev

# Refresh specific models
npm run db:refresh -- --model StagingData Facility Floor Room
```

## Configuration API

### Agent Configuration

#### Agent Model Schema
```javascript
{
  agent_id: "UUID",           // Primary key
  name: "string",             // Unique agent identifier
  display_name: "string",     // Human-readable name
  description: "text",        // Agent description
  source: "string",           // Source type (Local, FTP, S3, Azure, URL, API)
  type: "string",             // Agent type (Inbound, Outbound)
  handler: "string",          // Handler function name
  mapping: "string",          // Mapping file reference
  queue: "string",            // RabbitMQ queue name
  schema: "string",           // Database model for outbound agents
  stagging_key: "string",     // Key for deduplication
  batch_size: "integer",      // Processing batch size
  status: "boolean",          // Active/inactive status
  created_at: "timestamp",    // Creation timestamp
  updated_at: "timestamp"     // Last update timestamp
}
```

#### Agent Settings Schema
```javascript
{
  agent_setting_id: "UUID",  // Primary key
  agent_id: "UUID",          // Foreign key to agent
  key: "string",             // Setting key
  value: "text",             // Setting value (encrypted if sensitive)
  key_name: "string",        // Human-readable key name
  description: "text",       // Setting description
  is_encrypted: "boolean",   // Encryption flag
  created_at: "timestamp",   // Creation timestamp
  updated_at: "timestamp"    // Last update timestamp
}
```

### Caching Helper Functions

#### `getCachedAgentConfigs(options)`
Retrieves agent configurations with caching.

**Parameters:**
```javascript
{
  agentName: "string",       // Optional: specific agent name
  type: "string",            // Optional: filter by type (Inbound/Outbound)
  source: "string",          // Optional: filter by source
  status: "boolean"          // Optional: filter by status (default: true)
}
```

**Returns:**
```javascript
// Single agent (when agentName provided)
{
  agent_id: "uuid",
  name: "local_connection_batch_100",
  type: "Inbound",
  source: "Local",
  handler: "sendHrData",
  mapping: "hrData",
  queue: "hr_csv_data",
  batch_size: 100,
  status: true,
  settingsObj: {
    directory_path: "E:\\Windows\\Desktop\\care\\csv\\batch100"
  }
}

// Array of agents (when no agentName provided)
[
  { /* agent object */ },
  { /* agent object */ }
]
```

#### `getCachedAgentSources()`
Retrieves all active agent sources for queue management.

**Returns:**
```javascript
[
  {
    name: "local_connection_batch_100",
    source: "Local",
    queue: "hr_csv_data",
    settingsObj: {
      directory_path: "E:\\Windows\\Desktop\\care\\csv\\batch100"
    }
  }
]
```

#### `getCachedCronConfigsByName(cronName)`
Retrieves cron configuration by name.

**Parameters:**
- `cronName` (string): Name of the cron configuration

**Returns:**
```javascript
{
  name: "inbound_agent",
  display_name: "Inbound Agent Scheduler",
  description: "Scheduled processing for inbound agents",
  schedule: "0 */2 * * *",
  status: true
}
```

## Handler API

### Inbound Handler

#### `sendHrData(options)`
Processes CSV files and sends data to RabbitMQ.

**Parameters:**
```javascript
{
  file: "string",              // File path to process
  agent: "object",             // Agent configuration
  performanceMonitor: "object" // Optional performance monitor
}
```

**Returns:**
```javascript
{
  fileName: "string",
  totalRecords: "number",
  newRecords: "number",
  updatedRecords: "number",
  skippedRecords: "number",
  batchesSent: "number",
  batchSize: "number"
}
```

### Outbound Handlers

#### `generateCsv(options)`
Generates CSV files from database records.

**Parameters:**
```javascript
{
  eventCollection: "array",    // Collection of events to process
  agent: "object",             // Agent configuration
  mappingConfig: "object",     // Mapping configuration
  performanceMonitor: "object" // Optional performance monitor
}
```

**Returns:**
```javascript
{
  agentName: "string",
  totalEvents: "number",
  totalRecords: "number",
  successfulRecords: "number",
  failedRecords: "number",
  filesGenerated: "number",
  errors: "array",
  processedFiles: "array",
  messageResults: "array"
}
```

#### `sendApiData(options)`
Sends data to external APIs.

**Parameters:**
```javascript
{
  eventCollection: "array",    // Collection of events to process
  agent: "object",             // Agent configuration
  mappingConfig: "object",     // Mapping configuration
  performanceMonitor: "object" // Optional performance monitor
}
```

**Returns:**
```javascript
{
  agentName: "string",
  totalEvents: "number",
  totalRecords: "number",
  successfulRecords: "number",
  failedRecords: "number",
  apiCalls: "number",
  errors: "array",
  messageResults: "array"
}
```

#### `generateXml(options)`
Generates XML files from database records.

**Parameters:**
```javascript
{
  eventCollection: "array",    // Collection of events to process
  agent: "object",             // Agent configuration
  mappingConfig: "object",     // Mapping configuration
  performanceMonitor: "object" // Optional performance monitor
}
```

**Returns:**
```javascript
{
  agentName: "string",
  totalEvents: "number",
  totalRecords: "number",
  successfulRecords: "number",
  failedRecords: "number",
  filesGenerated: "number",
  errors: "array",
  processedFiles: "array",
  messageResults: "array"
}
```

## Service APIs

### CSV Service

#### `validateConfigs(agents)`
Validates agent configurations.

**Parameters:**
- `agents` (array): Array of agent configurations

**Returns:**
```javascript
[
  { /* valid agent config */ }
]
```

#### `processCSVFiles(agents, performanceMonitor)`
Processes CSV files for multiple agents.

**Parameters:**
- `agents` (array): Array of valid agent configurations
- `performanceMonitor` (object): Performance monitoring instance

**Returns:**
```javascript
{
  totalFilesProcessed: "number",
  totalRecordsProcessed: "number",
  totalErrors: "number",
  processingTime: "number"
}
```

### Event Service

#### `sendToRabbitMQ(eventPayload, performanceMonitor)`
Sends events to RabbitMQ queue.

**Parameters:**
```javascript
{
  queue: "string",             // Queue name
  agent: "object",             // Agent configuration
  batch: "array"               // Data batch to send
}
```

**Returns:**
- Promise resolves on successful send
- Promise rejects on error

### Performance Service

#### `PerformanceMonitor(jobName)`
Creates a new performance monitoring instance.

**Methods:**

##### `startStep(stepName, metadata)`
Starts timing a processing step.

**Parameters:**
- `stepName` (string): Name of the step
- `metadata` (object): Additional metadata

##### `endStep(stepName, results)`
Ends timing a processing step.

**Parameters:**
- `stepName` (string): Name of the step
- `results` (object): Step results

##### `logProgress(message, data)`
Logs progress information.

**Parameters:**
- `message` (string): Progress message
- `data` (object): Progress data

##### `complete(finalResults)`
Completes monitoring and generates report.

**Parameters:**
- `finalResults` (object): Final processing results

**Returns:**
```javascript
{
  jobName: "string",
  startTime: "timestamp",
  endTime: "timestamp",
  totalDuration: "number",
  steps: "array",
  finalResults: "object"
}
```

## Error Handling

### Error Types

#### `ValidationError`
Thrown when data validation fails.

```javascript
{
  name: "ValidationError",
  message: "string",
  field: "string",
  value: "any",
  rule: "string"
}
```

#### `ConfigurationError`
Thrown when agent configuration is invalid.

```javascript
{
  name: "ConfigurationError",
  message: "string",
  agentName: "string",
  missingFields: "array"
}
```

#### `ProcessingError`
Thrown when processing fails.

```javascript
{
  name: "ProcessingError",
  message: "string",
  agentName: "string",
  step: "string",
  originalError: "object"
}
```

#### `APIError`
Thrown when API calls fail.

```javascript
{
  name: "APIError",
  message: "string",
  statusCode: "number",
  response: "object",
  request: "object"
}
```

### Error Response Format

```javascript
{
  success: false,
  error: {
    type: "string",
    message: "string",
    code: "string",
    details: "object",
    timestamp: "string",
    requestId: "string"
  }
}
```

## Environment Variables

### Required Variables

```bash
# Database
NODE_ENV=production|development|test
DB_URL=postgresql://user:pass@host:port/database

# RabbitMQ
RABBITMQ_URL=amqp://user:pass@host:port
RABBITMQ_EXCHANGE=exchange_name

# Security
JWT_SECRET=your_jwt_secret
ENCRYPTION_KEY=32_character_key

# Logging
LOG_LEVEL=error|warn|info|debug
LOG_FILE_PATH=/path/to/logs
```

### Optional Variables

```bash
# Performance
MAX_BATCH_SIZE=1000
COLLECTION_TIMEOUT=5000
SHUTDOWN_TIMEOUT=30000
CONNECTION_POOL_SIZE=10

# Monitoring
METRICS_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_PORT=8080

# Features
ENABLE_CACHING=true
CACHE_TTL=300
ENABLE_ENCRYPTION=true
```

## Integration Examples

### Custom Agent Integration

```javascript
// custom-agent.js
const { getCachedAgentConfigs } = require('./helpers/caching.helper');
const handlers = require('./handlers');

async function runCustomAgent(agentName) {
  try {
    // Get agent configuration
    const agent = await getCachedAgentConfigs({ agentName });
    
    if (!agent || !agent.status) {
      throw new Error(`Agent ${agentName} not found or inactive`);
    }
    
    // Get handler
    const handler = handlers[agent.handler];
    if (!handler) {
      throw new Error(`Handler ${agent.handler} not found`);
    }
    
    // Execute handler
    const result = await handler(agent);
    console.log('Agent execution completed:', result);
    
  } catch (error) {
    console.error('Agent execution failed:', error);
    process.exit(1);
  }
}

// Usage
runCustomAgent('my_custom_agent');
```

### External API Integration

```javascript
// external-api-client.js
const axios = require('axios');

class CareMateAgentAPI {
  constructor(baseURL, apiKey) {
    this.client = axios.create({
      baseURL,
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
  }
  
  async triggerAgent(agentName) {
    const response = await this.client.post('/agents/trigger', {
      agentName
    });
    return response.data;
  }
  
  async getAgentStatus(agentName) {
    const response = await this.client.get(`/agents/${agentName}/status`);
    return response.data;
  }
  
  async getPerformanceMetrics(agentName, timeRange) {
    const response = await this.client.get(`/agents/${agentName}/metrics`, {
      params: { timeRange }
    });
    return response.data;
  }
}

// Usage
const api = new CareMateAgentAPI('http://localhost:3000', 'your-api-key');
const result = await api.triggerAgent('local_connection_batch_100');
```
