# Data Flow and Mapping Documentation

## Overview

The CareMate Agent System uses a sophisticated data transformation pipeline that handles bidirectional data flow between external sources and the CareMate database. This document details the data flow patterns, mapping configurations, and transformation processes.

## Data Flow Architecture

### Inbound Data Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   External      │    │   Inbound       │    │   CSV Parser    │
│   Source        │    │   Agent         │    │                 │
│                 │────┤                 │────┤ • Header detect │
│ • FTP/SFTP      │    │ • File fetch    │    │ • Row parsing   │
│ • S3/Azure      │    │ • Validation    │    │ • Type convert  │
│ • Local/URL     │    │ • Scheduling    │    │ • Error handle  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │   Processor     │    │   Staging       │
│                 │    │                 │    │                 │
│ • Identity      │◄───┤ • Rule engine   │◄───┤ • Mapping apply │
│ • Facility      │    │ • Event trigger │    │ • Deduplication │
│ • Access        │    │ • Validation    │    │ • Change detect │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Outbound Data Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │   Rule Engine   │    │   RabbitMQ      │
│                 │    │                 │    │                 │
│ • Data changes  │────┤ • Event detect  │────┤ • Event queue   │
│ • Triggers      │    │ • Rule eval     │    │ • Message batch │
│ • Schedules     │    │ • Event create  │    │ • Collection    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   External      │    │   Data          │    │   Outbound      │
│   Destination   │    │   Transform     │    │   Agent         │
│                 │    │                 │    │                 │
│ • API endpoint  │◄───┤ • Mapping apply │◄───┤ • Collection    │
│ • File system   │    │ • Format convert│    │ • Processing    │
│ • Cloud storage │    │ • Validation    │    │ • Error handle  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Mapping Types and Patterns

### 1. Inbound Mappings (CSV to Database)

#### Simple Field Mapping
Maps CSV columns to database fields using column names:

```json
{
  "Identity.email": "email",
  "Identity.first_name": "firstName",
  "Identity.last_name": "lastName",
  "Identity.middle_name": "middleName",
  "Identity.eid": "employeeId",
  "Identity.identity_type": "identityType",
  "Identity.national_id": "nationalId",
  "Identity.suffix": "suffix",
  "Identity.mobile": "mobile",
  "Identity.start_date": "startDate",
  "Identity.end_date": "endDate",
  "Identity.status": "status",
  "Identity.company": "company",
  "Identity.organization": "organization",
  "Identity.company_code": "companyCode",
  "Identity.job_title": "jobTitle",
  "Identity.job_code": "jobCode"
}
```

#### Positional Mapping
Maps CSV columns to database fields using column positions (0-based):

```json
{
  "Identity.email": "0",
  "Identity.first_name": "1",
  "Identity.last_name": "2",
  "Identity.middle_name": "3",
  "Identity.eid": "4",
  "Identity.identity_type": "5",
  "Identity.national_id": "6",
  "Identity.suffix": "7",
  "Identity.mobile": "8",
  "Identity.start_date": "9",
  "Identity.end_date": "10",
  "Identity.status": "11",
  "Identity.company": "12",
  "Identity.organization": "13",
  "Identity.company_code": "14",
  "Identity.job_title": "15",
  "Identity.job_code": "16"
}
```

### 2. Outbound Mappings (Database to External Format)

#### CSV Generation Mapping
Maps database fields to CSV column headers:

```json
{
  "mappings": {
    "Identity.identity_id": "Employee_ID",
    "Identity.first_name": "First_Name",
    "Identity.last_name": "Last_Name",
    "Identity.email": "Email_Address",
    "Identity.phone": "Phone_Number",
    "Identity.department": "Department",
    "Identity.position": "Job_Title",
    "Identity.employee_number": "Employee_Number",
    "Identity.hire_date": "Hire_Date",
    "Identity.status": "Status",
    "Identity.manager_id": "Manager_ID",
    "Identity.location": "Work_Location",
    "Identity.created_at": "Record_Created",
    "Identity.updated_at": "Record_Updated"
  }
}
```

#### API Transform Mapping
Complex JSON transformation for API endpoints:

```json
{
  "mappingType": "apiTransform",
  "apiConfig": {
    "method": "POST",
    "headers": {
      "Content-Type": "application/json",
      "X-API-Key": "{{api_key}}",
      "X-Client-ID": "{{client_id}}"
    },
    "timeout": 45000,
    "retries": 3,
    "retryDelay": 2000,
    "retryBackoff": "exponential"
  },
  "dataTransform": {
    "type": "object",
    "properties": {
      "employee": {
        "employeeId": "{{Identity.eid}}",
        "personalInfo": {
          "email": "{{Identity.email}}",
          "fullName": "{{Identity.first_name}} {{Identity.middle_name}} {{Identity.last_name}}",
          "firstName": "{{Identity.first_name}}",
          "lastName": "{{Identity.last_name}}",
          "nationalIdentifier": "{{Identity.national_id}}",
          "phoneNumber": "{{Identity.mobile}}"
        },
        "employment": {
          "startDate": "{{Identity.start_date}}",
          "endDate": "{{Identity.end_date}}",
          "status": "{{Identity.status}}",
          "companyName": "{{Identity.company}}",
          "organizationUnit": "{{Identity.organization}}",
          "companyCode": "{{Identity.company_code}}",
          "jobTitle": "{{Identity.job_title}}",
          "jobCode": "{{Identity.job_code}}"
        }
      },
      "batchInfo": {
        "batchId": "{{batch_id}}",
        "timestamp": "{{current_timestamp}}",
        "source": "caremate",
        "totalRecords": "{{batch_size}}"
      }
    }
  },
  "validation": {
    "required": ["employee.employeeId", "employee.personalInfo.email"],
    "rules": {
      "employee.personalInfo.email": {
        "type": "email",
        "required": true
      },
      "employee.personalInfo.firstName": {
        "type": "string",
        "required": true,
        "minLength": 1
      }
    }
  },
  "responseMapping": {
    "success": {
      "statusCodes": [200, 201],
      "responseFields": {
        "batchId": "response.data.batchId",
        "processedCount": "response.data.processedCount",
        "status": "response.status",
        "message": "response.message"
      }
    },
    "error": {
      "statusCodes": [400, 401, 403, 404, 422, 500],
      "errorFields": {
        "errorCode": "response.error.code",
        "errorMessage": "response.error.message",
        "failedRecords": "response.error.failedRecords"
      }
    }
  }
}
```

#### XML Transform Mapping
Structured XML generation for specialized systems:

```json
{
  "mappingType": "xmlTransform",
  "xmlConfig": {
    "rootElement": "CCURE9000Import",
    "xmlDeclaration": {
      "version": "1.0",
      "encoding": "UTF-8"
    },
    "namespace": "http://ccure9000.company.com/schema"
  },
  "dataTransform": {
    "ImportHeader": {
      "ImportId": "{{batch_id}}",
      "ImportDate": "{{current_timestamp}}",
      "Source": "CareMate_System",
      "Version": "1.0",
      "RecordCount": "{{batch_size}}"
    },
    "Personnel": [
      {
        "PersonnelId": "{{Identity.eid}}",
        "FirstName": "{{Identity.first_name}}",
        "LastName": "{{Identity.last_name}}",
        "MiddleName": "{{Identity.middle_name}}",
        "Email": "{{Identity.email}}",
        "NationalId": "{{Identity.national_id}}",
        "Mobile": "{{Identity.mobile}}",
        "Company": "{{Identity.company}}",
        "Organization": "{{Identity.organization}}",
        "CompanyCode": "{{Identity.company_code}}",
        "JobTitle": "{{Identity.job_title}}",
        "JobCode": "{{Identity.job_code}}",
        "StartDate": "{{Identity.start_date}}",
        "EndDate": "{{Identity.end_date}}",
        "Status": "{{Identity.status}}",
        "IdentityType": "{{Identity.identity_type}}",
        "Suffix": "{{Identity.suffix}}",
        "Manager": "{{Identity.manager}}",
        "CreatedDate": "{{current_timestamp}}",
        "UpdatedDate": "{{current_timestamp}}"
      }
    ],
    "Credentials": [
      {
        "CredentialId": "CARD_{{Identity.eid}}",
        "PersonnelId": "{{Identity.eid}}",
        "CredentialType": "ProximityCard",
        "CardNumber": "{{Identity.eid}}_CARD",
        "FacilityCode": "{{Identity.company_code}}",
        "Status": "Active",
        "IssueDate": "{{Identity.start_date}}",
        "ExpiryDate": "{{Identity.end_date}}",
        "CreatedDate": "{{current_timestamp}}"
      }
    ],
    "Clearances": [
      {
        "ClearanceId": "CLR_{{Identity.eid}}",
        "PersonnelId": "{{Identity.eid}}",
        "AccessLevel": "Standard",
        "FacilityCode": "{{Identity.company_code}}",
        "EffectiveDate": "{{Identity.start_date}}",
        "ExpirationDate": "{{Identity.end_date}}",
        "Status": "Active",
        "CreatedDate": "{{current_timestamp}}"
      }
    ],
    "ImportFooter": {
      "ProcessedRecords": "{{batch_size}}",
      "ProcessedDate": "{{current_timestamp}}",
      "Status": "Complete"
    }
  }
}
```

## Template Variables and Context

### Available Variables

#### Database Fields
- `{{Identity.field_name}}` - Any field from the Identity model
- `{{Facility.field_name}}` - Any field from related Facility model
- `{{AccessLevel.field_name}}` - Any field from related AccessLevel model

#### System Variables
- `{{current_timestamp}}` - Current ISO timestamp
- `{{batch_id}}` - Unique batch identifier (UUID)
- `{{batch_size}}` - Number of records in current batch
- `{{message_id}}` - Unique message identifier
- `{{event_index}}` - Index of current event in collection

#### Agent Settings
- `{{api_key}}` - Agent's API key setting
- `{{client_id}}` - Agent's client ID setting
- `{{api_url}}` - Agent's API URL setting
- Any other agent setting key

### Context Processing

The template engine processes variables in the following order:

1. **Database Record Fields**: Direct field access from the current record
2. **System Context**: Generated system variables
3. **Agent Settings**: Configuration values from agent settings
4. **Default Values**: Fallback to empty string if variable not found

### String Interpolation Examples

```javascript
// Simple field replacement
"{{Identity.first_name}}" → "John"

// Concatenation
"{{Identity.first_name}} {{Identity.last_name}}" → "John Doe"

// Nested object creation
{
  "name": "{{Identity.first_name}} {{Identity.last_name}}",
  "contact": {
    "email": "{{Identity.email}}",
    "phone": "{{Identity.mobile}}"
  }
}

// Array processing (for XML)
"Personnel": [
  {
    "PersonnelId": "{{Identity.eid}}",
    "Name": "{{Identity.first_name}} {{Identity.last_name}}"
  }
]
```

## Data Validation and Quality

### Inbound Validation

#### Required Field Validation
```javascript
const requiredFields = ['email', 'first_name', 'last_name'];
const missingFields = requiredFields.filter(field => !record[field]);
if (missingFields.length > 0) {
  throw new ValidationError(`Missing required fields: ${missingFields.join(', ')}`);
}
```

#### Data Type Validation
```javascript
// Email validation
if (record.email && !isValidEmail(record.email)) {
  throw new ValidationError('Invalid email format');
}

// Date validation
if (record.start_date && !isValidDate(record.start_date)) {
  throw new ValidationError('Invalid start date format');
}
```

#### Duplicate Detection
```javascript
// Based on staging key (usually email)
const existingRecord = await StagingData.findOne({
  where: { [agent.stagging_key]: record[agent.stagging_key] }
});

if (existingRecord) {
  // Handle duplicate based on business rules
  if (isRecordUpdated(existingRecord, record)) {
    // Update existing record
  } else {
    // Skip duplicate
  }
}
```

### Outbound Validation

#### API Payload Validation
```javascript
const validationRules = mappingConfig.validation;
if (validationRules) {
  const errors = validatePayload(transformedData, validationRules);
  if (errors.length > 0) {
    throw new ValidationError(`Payload validation failed: ${errors.join(', ')}`);
  }
}
```

#### Response Validation
```javascript
const responseMapping = mappingConfig.responseMapping;
if (responseMapping) {
  const isSuccess = responseMapping.success.statusCodes.includes(response.status);
  if (!isSuccess) {
    const errorInfo = extractErrorInfo(response, responseMapping.error);
    throw new APIError(`API call failed: ${errorInfo.message}`);
  }
}
```

## Performance Optimization

### Mapping Caching
```javascript
const mappingCache = new Map();

function loadMappingConfig(mappingName) {
  if (mappingCache.has(mappingName)) {
    return mappingCache.get(mappingName);
  }
  
  const config = require(`../mappings/${mappingName}.mapping.json`);
  mappingCache.set(mappingName, config);
  return config;
}
```

### Bulk Processing
```javascript
// Process records in batches for better performance
const batchSize = agent.batch_size || 100;
for (let i = 0; i < records.length; i += batchSize) {
  const batch = records.slice(i, i + batchSize);
  await processBatch(batch, mappingConfig);
}
```

### Memory Management
```javascript
// Stream processing for large files
const csvStream = csv.parseFile(filePath, { headers: true })
  .on('data', (row) => {
    // Process row immediately
    processRow(row, mappingConfig);
  })
  .on('end', () => {
    // Cleanup
  });
```

## Error Handling and Recovery

### Mapping Errors
- **Missing Fields**: Log warning and use default values
- **Type Conversion**: Attempt conversion or use null
- **Template Errors**: Log error and skip record
- **Validation Failures**: Reject record with detailed error

### Recovery Strategies
- **Partial Success**: Process valid records, report failures
- **Retry Logic**: Retry failed transformations with backoff
- **Manual Review**: Queue failed records for manual processing
- **Rollback**: Revert changes on critical failures

### Error Reporting
```javascript
const transformationErrors = [];

records.forEach((record, index) => {
  try {
    const transformed = transformRecord(record, mappingConfig);
    processedRecords.push(transformed);
  } catch (error) {
    transformationErrors.push({
      recordIndex: index,
      record,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Generate error report
if (transformationErrors.length > 0) {
  await generateErrorReport(transformationErrors);
}
```
