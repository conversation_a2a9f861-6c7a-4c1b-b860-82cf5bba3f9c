# CareMate Agent System Architecture

## Overview

The CareMate Agent System is a dynamic, event-driven data integration platform that facilitates bidirectional data flow between the CareMate system and external sources. The system is designed around two primary agent types: **Inbound Agents** (data ingestion) and **Outbound Agents** (data distribution).

## System Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           CAREMATE AGENT SYSTEM                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐             │
│  │   INBOUND       │    │   RABBITMQ      │    │   OUTBOUND      │             │
│  │   AGENTS        │    │   MESSAGE       │    │   AGENTS        │             │
│  │                 │    │   BROKER        │    │                 │             │
│  │ ┌─────────────┐ │    │                 │    │ ┌─────────────┐ │             │
│  │ │ FTP/SFTP    │ │    │ ┌─────────────┐ │    │ │ CSV Export  │ │             │
│  │ │ Sources     │ │────┤ │hr_csv_data  │ │    │ │ Handler     │ │             │
│  │ └─────────────┘ │    │ │   Queue     │ │    │ └─────────────┘ │             │
│  │                 │    │ └─────────────┘ │    │                 │             │
│  │ ┌─────────────┐ │    │                 │    │ ┌─────────────┐ │             │
│  │ │ AWS S3      │ │    │ ┌─────────────┐ │    │ │ API         │ │             │
│  │ │ Sources     │ │────┤ │api_1_out    │ │────┤ │ Outbound    │ │             │
│  │ └─────────────┘ │    │ │bound_queue  │ │    │ │ Handler     │ │             │
│  │                 │    │ └─────────────┘ │    │ └─────────────┘ │             │
│  │ ┌─────────────┐ │    │                 │    │                 │             │
│  │ │ Azure Blob  │ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │             │
│  │ │ Sources     │ │────┤ │api_2_out    │ │────┤ │ XML         │ │             │
│  │ └─────────────┘ │    │ │bound_queue  │ │    │ │ Generator   │ │             │
│  │                 │    │ └─────────────┘ │    │ │ Handler     │ │             │
│  │ ┌─────────────┐ │    │                 │    │ └─────────────┘ │             │
│  │ │ Local       │ │    │ ┌─────────────┐ │    │                 │             │
│  │ │ Directory   │ │────┤ │csv_gen_out  │ │────┤                 │             │
│  │ └─────────────┘ │    │ │bound_queue  │ │    │                 │             │
│  │                 │    │ └─────────────┘ │    │                 │             │
│  │ ┌─────────────┐ │    │                 │    │                 │             │
│  │ │ URL/API     │ │    │ ┌─────────────┐ │    │                 │             │
│  │ │ Sources     │ │────┤ │ccure9000_xml│ │────┤                 │             │
│  │ └─────────────┘ │    │ │_out_queue   │ │    │                 │             │
│  └─────────────────┘    │ └─────────────┘ │    │                 │             │
│                         └─────────────────┘    └─────────────────┘             │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                              PROCESSOR SYSTEM                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐             │
│  │   STAGING       │    │   RULE ENGINE   │    │   DATABASE      │             │
│  │   DATA          │    │                 │    │                 │             │
│  │                 │    │ ┌─────────────┐ │    │ ┌─────────────┐ │             │
│  │ ┌─────────────┐ │    │ │ Identity    │ │    │ │ Identity    │ │             │
│  │ │ CSV Data    │ │────┤ │ Rules       │ │────┤ │ Table       │ │             │
│  │ │ Processing  │ │    │ └─────────────┘ │    │ └─────────────┘ │             │
│  │ └─────────────┘ │    │                 │    │                 │             │
│  │                 │    │ ┌─────────────┐ │    │ ┌─────────────┐ │             │
│  │ ┌─────────────┐ │    │ │ Event       │ │    │ │ Facility    │ │             │
│  │ │ Data        │ │────┤ │ Generation  │ │────┤ │ Tables      │ │             │
│  │ │ Validation  │ │    │ └─────────────┘ │    │ └─────────────┘ │             │
│  │ └─────────────┘ │    │                 │    │                 │             │
│  │                 │    │ ┌─────────────┐ │    │ ┌─────────────┐ │             │
│  │ ┌─────────────┐ │    │ │ Outbound    │ │    │ │ Access      │ │             │
│  │ │ Mapping     │ │────┤ │ Event       │ │────┤ │ Control     │ │             │
│  │ │ Transform   │ │    │ │ Triggers    │ │    │ │ Tables      │ │             │
│  │ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │             │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘             │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Agent Types

#### Inbound Agents
- **Purpose**: Fetch data from external sources and process it into the CareMate system
- **Sources Supported**:
  - FTP/SFTP servers
  - AWS S3 buckets
  - Azure Blob Storage
  - Local directories
  - HTTP/HTTPS URLs
- **Processing Flow**: Source → CSV Processing → Staging → Database
- **Handler**: `sendHrData`

#### Outbound Agents
- **Purpose**: Send data from CareMate system to external destinations
- **Types**:
  - **CSV Outbound**: Generate CSV files from database models
  - **API Outbound**: Send data to external systems via REST APIs
  - **XML Outbound**: Generate XML files for specialized systems (e.g., CCURE9000)
- **Handlers**: `generateCsv`, `sendApiData`, `generateXml`

### 2. Dynamic Configuration System

All agents are configured through database seeders, making the system completely dynamic:

```
Agent Configuration:
├── Agent Table (agent.model.js)
│   ├── name (unique identifier)
│   ├── type (Inbound/Outbound)
│   ├── source (Local/FTP/S3/Azure/URL/API)
│   ├── handler (sendHrData/generateCsv/sendApiData/generateXml)
│   ├── mapping (reference to mapping file)
│   ├── queue (RabbitMQ queue name)
│   ├── schema (database model for outbound)
│   └── status (active/inactive)
└── Agent Settings Table (agentSetting.model.js)
    ├── Encrypted connection details
    ├── API credentials
    ├── Directory paths
    └── Configuration parameters
```

### 3. Message Queue Architecture

The system uses RabbitMQ for asynchronous message processing:

- **Inbound Flow**: External Source → Agent → RabbitMQ → Processor → Database
- **Outbound Flow**: Rule Engine → RabbitMQ → Agent → External Destination
- **Collection Processing**: Messages are batched for optimal performance
- **Error Handling**: Failed messages are requeued with retry logic

### 4. Mapping System

Data transformation is handled through JSON mapping files:

#### Inbound Mappings (CSV to Database)
```json
{
  "Identity.email": "email",
  "Identity.first_name": "firstName",
  "Identity.last_name": "lastName"
}
```

#### Outbound Mappings (Database to External Format)
```json
{
  "mappings": {
    "Identity.identity_id": "Employee_ID",
    "Identity.first_name": "First_Name",
    "Identity.email": "Email_Address"
  }
}
```

#### API Mappings (Database to API Format)
```json
{
  "mappingType": "apiTransform",
  "apiConfig": {
    "method": "POST",
    "headers": {
      "Content-Type": "application/json",
      "X-API-Key": "{{api_key}}"
    }
  },
  "dataTransform": {
    "employee": {
      "id": "{{Identity.eid}}",
      "email": "{{Identity.email}}"
    }
  }
}
```

## Performance Monitoring

The system includes comprehensive performance tracking:

- **File Processing Metrics**: Processing time, record counts, success/failure rates
- **API Performance**: Response times, status codes, retry attempts
- **Queue Metrics**: Message throughput, processing delays
- **Error Tracking**: Detailed error logs with context
- **Performance Reports**: Saved to `/performances/` directory

## Security Features

- **Encrypted Settings**: Sensitive configuration data is encrypted in the database
- **Authentication Support**: Multiple authentication methods for APIs
- **Access Control**: Role-based permissions for agent management
- **Audit Trail**: Complete logging of all agent activities

## Scalability Design

- **Horizontal Scaling**: Multiple agent instances can run simultaneously
- **Queue-Based Processing**: Asynchronous processing prevents bottlenecks
- **Batch Processing**: Configurable batch sizes for optimal performance
- **Resource Management**: Automatic cleanup and connection pooling
