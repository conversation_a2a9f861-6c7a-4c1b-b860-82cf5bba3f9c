# Outbound Agents Documentation

## Overview

Outbound agents are responsible for sending data from the CareMate system to external destinations. They support multiple output formats and destinations, using a collection-based processing approach for optimal performance.

## Agent Types

### 1. CSV Outbound Agents
- **Handler**: `generateCsv`
- **Purpose**: Generate CSV files from database models
- **Destinations**: Local directories, FTP/SFTP, S3, Azure Blob
- **Features**:
  - Header/positional mapping support
  - Bulk data export
  - File compression
  - Automatic archiving

### 2. API Outbound Agents
- **Handler**: `sendApiData`
- **Purpose**: Send data to external systems via REST APIs
- **Authentication**: API keys, OAuth tokens, client credentials
- **Features**:
  - Real-time data transmission
  - Retry logic with exponential backoff
  - Response validation
  - Performance monitoring

### 3. XML Outbound Agents
- **Handler**: `generateXml`
- **Purpose**: Generate XML files for specialized systems
- **Use Cases**: CCURE9000 integration, legacy system imports
- **Features**:
  - Custom XML schema support
  - Namespace handling
  - One file per event
  - Validation against XSD

## Processing Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Rule Engine   │    │   RabbitMQ      │    │   Collection    │
│                 │    │                 │    │   Processing    │
│ • Event trigger │────┤ • Event queue   │────┤                 │
│ • Data changes  │    │ • Batch collect │    │ • Message batch │
│ • Schedule      │    │ • Timeout mgmt  │    │ • Optimization  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   External      │    │   Data          │    │   Handler       │
│   Destination   │    │   Transform     │    │   Execution     │
│                 │    │                 │    │                 │
│ • API endpoint  │◄───┤ • Mapping apply │◄───┤ • CSV/API/XML   │
│ • File system   │    │ • Validation    │    │ • Error handle  │
│ • Cloud storage │    │ • Format convert│    │ • Performance   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Collection-Based Processing

Outbound agents use a sophisticated collection approach to optimize performance:

### Collection Strategy
- **Timeout-Based**: Messages collected for configurable duration (default: 5 seconds)
- **Batch Processing**: Process multiple events together
- **Token Optimization**: Reuse authentication tokens across requests
- **Resource Efficiency**: Minimize database queries and API calls

### Configuration
```javascript
const COLLECTION_TIMEOUT = 5000; // 5 seconds
const SHUTDOWN_TIMEOUT = 30000;  // 30 seconds for graceful shutdown
```

### Processing Flow
1. **Message Collection**: Accumulate messages in memory
2. **Timeout Trigger**: Process collection after timeout
3. **Bulk Processing**: Handle all messages in batch
4. **Individual ACK**: Acknowledge each message separately

## Agent Configuration Examples

### CSV Outbound Agent
```javascript
{
  name: 'local_connection_outbound_batch_100',
  display_name: 'HR Data Outbound Local Directory',
  description: 'Local directory for outbound CSV generation',
  source: 'Local',
  type: 'Outbound',
  handler: 'generateCsv',
  mapping: 'csv_generation_outbound',
  queue: 'csv_generation_outbound_queue',
  schema: 'Identity',
  batch_size: 100,
  settings: {
    directory_path: 'E:\\Windows\\Desktop\\care\\csv\\outbound'
  }
}
```

### API Outbound Agent (API Key Authentication)
```javascript
{
  name: 'api_1_outbound',
  display_name: 'External Partner API Outbound',
  description: 'Send employee data to external partner via API',
  source: 'API',
  type: 'Outbound',
  handler: 'sendApiData',
  mapping: 'api1Outbound',
  queue: 'api_1_outbound_queue',
  schema: 'Identity',
  batch_size: 50,
  settings: {
    api_url: 'http://localhost:3051/log',
    api_key: 'your_api_key_here',
    client_id: 'caremate_client',
    timeout: 45000,
    retries: 3
  }
}
```

### API Outbound Agent (Token Authentication)
```javascript
{
  name: 'api_2_outbound',
  display_name: 'Government System API Outbound',
  description: 'Send employee data to government system via API with token authentication',
  source: 'API',
  type: 'Outbound',
  handler: 'sendApiData',
  mapping: 'api2Outbound',
  queue: 'api_2_outbound_queue',
  schema: 'Identity',
  batch_size: 25,
  settings: {
    api_url: 'http://localhost:3052/log',
    token_url: 'http://localhost:3052/auth/token',
    client_id: 'caremate_gov_client',
    client_secret: 'super_secret_key_123',
    grant_type: 'client_credentials',
    scope: 'employee_data_write',
    retries: 5
  }
}
```

### XML Outbound Agent
```javascript
{
  name: 'ccure9000_xml_outbound_local',
  display_name: 'CCURE9000 XML Local Drop',
  description: 'Generate XML files for CCURE9000 personnel, credentials, and clearances import',
  source: 'Local',
  type: 'Outbound',
  handler: 'generateXml',
  mapping: 'ccure9000Outbound',
  queue: 'ccure9000_xml_outbound_queue',
  schema: 'Identity',
  batch_size: 100,
  settings: {
    directory_path: 'E:\\Windows\\Desktop\\care\\csv\\outbound_xml_drop'
  }
}
```

## Mapping Configurations

### CSV Mapping (Column Headers)
```json
{
  "mappings": {
    "Identity.identity_id": "Employee_ID",
    "Identity.first_name": "First_Name",
    "Identity.last_name": "Last_Name",
    "Identity.email": "Email_Address",
    "Identity.phone": "Phone_Number",
    "Identity.department": "Department",
    "Identity.position": "Job_Title",
    "Identity.employee_number": "Employee_Number",
    "Identity.hire_date": "Hire_Date",
    "Identity.status": "Status",
    "Identity.manager_id": "Manager_ID",
    "Identity.location": "Work_Location",
    "Identity.created_at": "Record_Created",
    "Identity.updated_at": "Record_Updated"
  }
}
```

### API Mapping (JSON Transform)
```json
{
  "mappingType": "apiTransform",
  "apiConfig": {
    "method": "POST",
    "headers": {
      "Content-Type": "application/json",
      "X-API-Key": "{{api_key}}",
      "X-Client-ID": "{{client_id}}"
    },
    "timeout": 45000,
    "retries": 3,
    "retryDelay": 2000,
    "retryBackoff": "exponential"
  },
  "dataTransform": {
    "type": "object",
    "properties": {
      "employee": {
        "employeeId": "{{Identity.eid}}",
        "personalInfo": {
          "email": "{{Identity.email}}",
          "fullName": "{{Identity.first_name}} {{Identity.middle_name}} {{Identity.last_name}}",
          "firstName": "{{Identity.first_name}}",
          "lastName": "{{Identity.last_name}}",
          "nationalIdentifier": "{{Identity.national_id}}",
          "phoneNumber": "{{Identity.mobile}}"
        },
        "employment": {
          "startDate": "{{Identity.start_date}}",
          "endDate": "{{Identity.end_date}}",
          "status": "{{Identity.status}}",
          "companyName": "{{Identity.company}}",
          "organizationUnit": "{{Identity.organization}}",
          "companyCode": "{{Identity.company_code}}",
          "jobTitle": "{{Identity.job_title}}",
          "jobCode": "{{Identity.job_code}}"
        }
      },
      "batchInfo": {
        "batchId": "{{batch_id}}",
        "timestamp": "{{current_timestamp}}",
        "source": "caremate",
        "totalRecords": "{{batch_size}}"
      }
    }
  },
  "validation": {
    "required": ["employee.employeeId", "employee.personalInfo.email"],
    "rules": {
      "employee.personalInfo.email": {
        "type": "email",
        "required": true
      }
    }
  }
}
```

### XML Mapping (CCURE9000)
```json
{
  "mappingType": "xmlTransform",
  "xmlConfig": {
    "rootElement": "CCURE9000Import",
    "xmlDeclaration": {
      "version": "1.0",
      "encoding": "UTF-8"
    }
  },
  "dataTransform": {
    "ImportHeader": {
      "ImportId": "{{batch_id}}",
      "ImportDate": "{{current_timestamp}}",
      "Source": "CareMate_System",
      "Version": "1.0",
      "RecordCount": "{{batch_size}}"
    },
    "Personnel": [
      {
        "PersonnelId": "{{Identity.eid}}",
        "FirstName": "{{Identity.first_name}}",
        "LastName": "{{Identity.last_name}}",
        "MiddleName": "{{Identity.middle_name}}",
        "Email": "{{Identity.email}}",
        "NationalId": "{{Identity.national_id}}",
        "Mobile": "{{Identity.mobile}}",
        "Company": "{{Identity.company}}",
        "Organization": "{{Identity.organization}}",
        "CompanyCode": "{{Identity.company_code}}",
        "JobTitle": "{{Identity.job_title}}",
        "JobCode": "{{Identity.job_code}}",
        "StartDate": "{{Identity.start_date}}",
        "EndDate": "{{Identity.end_date}}",
        "Status": "{{Identity.status}}",
        "CreatedDate": "{{current_timestamp}}",
        "UpdatedDate": "{{current_timestamp}}"
      }
    ],
    "Credentials": [
      {
        "CredentialId": "CARD_{{Identity.eid}}",
        "PersonnelId": "{{Identity.eid}}",
        "CredentialType": "ProximityCard",
        "CardNumber": "{{Identity.eid}}_CARD",
        "FacilityCode": "{{Identity.company_code}}",
        "Status": "Active",
        "IssueDate": "{{Identity.start_date}}",
        "ExpiryDate": "{{Identity.end_date}}",
        "CreatedDate": "{{current_timestamp}}"
      }
    ]
  }
}
```

## Authentication Patterns

### API Key Authentication
- Simple header-based authentication
- Static API key configuration
- Suitable for basic integrations

### Token-Based Authentication
- OAuth 2.0 client credentials flow
- Dynamic token generation and refresh
- Token caching with expiration handling
- Suitable for enterprise integrations

### Token Management
```javascript
// Token caching strategy
const tokenCache = new Map();
const TOKEN_SAFETY_MARGIN = 300; // 5 minutes before expiry

async function getAuthToken(settings, agentId) {
  const cacheKey = agentId;
  const cachedToken = tokenCache.get(cacheKey);
  
  if (cachedToken && cachedToken.expiresAt > Date.now() + TOKEN_SAFETY_MARGIN * 1000) {
    return cachedToken.token;
  }
  
  // Generate new token
  const newToken = await generateToken(settings);
  tokenCache.set(cacheKey, {
    token: newToken.access_token,
    expiresAt: Date.now() + (newToken.expires_in * 1000)
  });
  
  return newToken.access_token;
}
```

## Performance Monitoring

### API Performance Metrics
- Response times (min, max, average)
- Success/failure rates
- Status code distribution
- Error types and frequencies
- Retry attempts and timeouts
- API throughput metrics

### CSV/XML Performance Metrics
- File generation time
- Record processing rate
- File size metrics
- Upload/transfer times
- Error rates

### Collection Performance
- Collection size distribution
- Processing time per collection
- Message acknowledgment rates
- Queue depth monitoring

## Error Handling

### API Errors
- **4xx Errors**: Client errors (authentication, validation)
- **5xx Errors**: Server errors (temporary failures)
- **Network Errors**: Connection timeouts, DNS failures
- **Rate Limiting**: Backoff and retry strategies

### File Generation Errors
- **Disk Space**: Insufficient storage
- **Permissions**: File system access issues
- **Data Validation**: Invalid data formats
- **Template Errors**: Mapping configuration issues

### Recovery Strategies
- **Exponential Backoff**: Increasing retry delays
- **Circuit Breaker**: Temporary service suspension
- **Dead Letter Queue**: Failed message handling
- **Manual Intervention**: Admin notifications

## Running Outbound Agents

### Command Line
```bash
# CSV outbound agent
npm run outbound_csv

# API outbound agents
npm run outbound_api_1
npm run outbound_api_2

# XML outbound agent
npm run outbound_xml

# Direct execution
node index.js --agent api_1_outbound
```

### Testing
```bash
# Start test API servers
npm run test_api_1  # Port 3051
npm run test_api_2  # Port 3052

# Run continuous testing
npm run test_api_2  # Continuous event generation
```

### Monitoring
- Check `/performances/` directory for detailed reports
- Monitor RabbitMQ queue depths
- Review API response logs
- Track file generation in output directories
