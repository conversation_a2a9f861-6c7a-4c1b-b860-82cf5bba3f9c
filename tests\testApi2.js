const express = require('express');
const fs = require('fs');
const path = require('path');
const { createLogger, format, transports } = require('winston');
require('winston-daily-rotate-file');
const expressWinston = require('express-winston');

const app = express();
const PORT = 3052;

// Ensure logs directory exists
const logDirectory = path.join(__dirname, 'logs2');
if (!fs.existsSync(logDirectory)) {
  fs.mkdirSync(logDirectory);
}

// Winston setup with daily rotation
const dailyRotateTransport = new transports.DailyRotateFile({
  filename: path.join(logDirectory, 'app-%DATE%.log'),
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '14d'
});

const logger = createLogger({
  level: 'info',
  format: format.combine(
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    format.printf(({ timestamp, level, message, ...meta }) => {
      const metaString = Object.keys(meta).length
        ? ` ${JSON.stringify(meta)}`
        : '';
      return `${timestamp} [${level}]: ${message}${metaString}`;
    })
  ),
  transports: [
    new transports.Console(),
    dailyRotateTransport
  ],
  exitOnError: false
});

// Body parsers
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// HTTP request logging
app.use(expressWinston.logger({
  winstonInstance: logger,
  meta: true,
  msg: '{{req.method}} {{req.url}} {{res.statusCode}} - {{res.responseTime}}ms',
  expressFormat: false,
  colorize: false,
}));

// POST /auth/token - generate test access tokens
app.post('/auth/token', (req, res) => {
  const { client_id, client_secret, grant_type, scope } = req.body;

  // Validate required fields
  if (!client_id || !client_secret || !grant_type) {
    logger.warn('Missing required parameters on /auth/token', { body: req.body });
    return res.status(400).json({
      error: 'invalid_request',
      error_description: 'Missing required parameters: client_id, client_secret, grant_type'
    });
  }

  // Validate credentials
  if (client_id !== 'caremate_gov_client' || client_secret !== 'super_secret_key_123') {
    logger.error('Invalid client credentials', { client_id });
    return res.status(401).json({
      error: 'invalid_client',
      error_description: 'Invalid client credentials'
    });
  }

  // Validate grant type
  if (grant_type !== 'client_credentials') {
    logger.warn('Unsupported grant type', { grant_type });
    return res.status(400).json({
      error: 'unsupported_grant_type',
      error_description: 'Only client_credentials grant type is supported'
    });
  }

  // Generate mock token
  const accessToken = `gov_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const expiresIn = 3600;

  logger.info('Generated access token', { client_id, scope: scope || 'default' });
  res.json({
    access_token: accessToken,
    token_type: 'Bearer',
    expires_in: expiresIn,
    scope: scope || 'employee_data_write'
  });
});

// POST /log - logs incoming JSON data
app.post('/log', (req, res) => {
  const data = req.body;
  const authHeader = req.headers.authorization;

  // Log token if present
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const tokenSnippet = authHeader.slice(7, 27) + '...';
    logger.info('Request with Bearer token', { token: tokenSnippet });
  }

  if (!data || Object.keys(data).length === 0) {
    logger.warn('POST /log called with empty body');
    return res.status(400).json({ error: 'No data provided' });
  }

  const timestamp = new Date().toISOString();
  const logEntry = `${timestamp} - ${JSON.stringify(data)}\n`;
  logger.info('Logging data payload', { sample: logEntry.substring(0, 100) });

  fs.appendFile(path.join(logDirectory, 'app-manual.log'), logEntry, (err) => {
    if (err) {
      logger.error('Failed to write manual log file', { error: err });
      return res.status(500).json({ error: 'Internal server error' });
    }

    const recordCount = Array.isArray(data.employees) ? data.employees.length : 1;
    res.json({
      message: 'Data logged successfully',
      timestamp,
      recordCount
    });
  });
});

// Error-logging middleware
app.use(expressWinston.errorLogger({
  winstonInstance: logger
}));

// Start server
app.listen(PORT, () => {
  logger.info(`Server listening on http://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  logger.info('Shutting down server...');
  process.exit(0);
});
