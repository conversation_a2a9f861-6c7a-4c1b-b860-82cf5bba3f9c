# CareMate Agent System Documentation

## Overview

The CareMate Agent System is a comprehensive, dynamic data integration platform designed to facilitate seamless bidirectional data flow between the CareMate healthcare management system and external sources. Built with Node.js and leveraging RabbitMQ for message queuing, the system provides robust, scalable, and secure data processing capabilities.

## Key Features

### 🔄 Bidirectional Data Flow
- **Inbound Agents**: Fetch data from external sources (FTP, S3, Azure, Local, URL)
- **Outbound Agents**: Send data to external destinations (CSV, API, XML formats)

### 🎯 Dynamic Configuration
- Database-driven agent configuration
- No code changes required for new integrations
- Encrypted sensitive settings
- Real-time configuration updates

### 📊 Multiple Data Formats
- **CSV Processing**: Header and positional mapping support
- **API Integration**: REST API with multiple authentication methods
- **XML Generation**: Custom schema support for specialized systems

### ⚡ High Performance
- Collection-based processing for optimal throughput
- Configurable batch sizes
- Asynchronous message processing
- Connection pooling and caching

### 🔒 Enterprise Security
- Encrypted configuration storage
- Multiple authentication patterns
- Audit trails and logging
- Role-based access control

### 📈 Comprehensive Monitoring
- Real-time performance metrics
- Detailed error tracking
- Health checks and alerts
- Performance report generation

## System Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           CAREMATE AGENT SYSTEM                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐             │
│  │   INBOUND       │    │   RABBITMQ      │    │   OUTBOUND      │             │
│  │   AGENTS        │    │   MESSAGE       │    │   AGENTS        │             │
│  │                 │    │   BROKER        │    │                 │             │
│  │ • FTP/SFTP      │────┤ • Event Queues  │────┤ • CSV Export    │             │
│  │ • AWS S3        │    │ • Batch Process │    │ • API Calls     │             │
│  │ • Azure Blob    │    │ • Error Handle  │    │ • XML Generate  │             │
│  │ • Local Files   │    │ • Collection    │    │ • File Upload   │             │
│  │ • URL Sources   │    │ • Retry Logic   │    │ • Validation    │             │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘             │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│                              PROCESSOR SYSTEM                                  │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐             │
│  │   STAGING       │    │   RULE ENGINE   │    │   DATABASE      │             │
│  │   DATA          │    │                 │    │                 │             │
│  │ • Validation    │────┤ • Event Trigger │────┤ • Identity      │             │
│  │ • Deduplication │    │ • Rule Eval     │    │ • Facility      │             │
│  │ • Mapping       │    │ • Data Transform│    │ • Access        │             │
│  │ • Change Detect │    │ • Outbound Send │    │ • Audit Trail   │             │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘             │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Quick Start

### Prerequisites
- Node.js v18.0.0 or higher
- PostgreSQL v13.0 or higher
- RabbitMQ v3.8.0 or higher

### Installation

1. **Clone the repository**
   ```bash
   git clone https://git.onetalkhub.com/care/care-agent.git
   cd care-agent
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Setup database**
   ```bash
   npm run db
   npx sequelize-cli db:seed:all
   ```

5. **Run an agent**
   ```bash
   # Run inbound agent
   node index.js --agent local_connection_batch_100
   
   # Run outbound agent
   node index.js --agent api_1_outbound
   ```

## Documentation Structure

### 📋 Core Documentation
- **[System Architecture](AGENT_SYSTEM_ARCHITECTURE.md)** - Complete system overview and architecture
- **[Inbound Agents](INBOUND_AGENTS.md)** - Data ingestion from external sources
- **[Outbound Agents](OUTBOUND_AGENTS.md)** - Data distribution to external destinations
- **[Data Flow & Mapping](DATA_FLOW_AND_MAPPING.md)** - Data transformation and mapping patterns

### 🛠️ Operations & Development
- **[Deployment & Operations](DEPLOYMENT_AND_OPERATIONS.md)** - Production deployment and monitoring
- **[API Reference](API_REFERENCE.md)** - Complete API and CLI reference

## Agent Types

### Inbound Agents
Process data from external sources into the CareMate system:

| Source Type | Description | Configuration |
|-------------|-------------|---------------|
| **FTP/SFTP** | Secure file transfer protocols | Host, credentials, paths |
| **AWS S3** | Amazon S3 bucket integration | Access keys, bucket, region |
| **Azure Blob** | Microsoft Azure storage | Connection string, container |
| **Local Directory** | File system monitoring | Directory path, permissions |
| **URL/HTTP** | Web-based data sources | URL, authentication headers |

### Outbound Agents
Send data from CareMate to external destinations:

| Handler Type | Description | Output Format |
|--------------|-------------|---------------|
| **generateCsv** | CSV file generation | Structured CSV with headers |
| **sendApiData** | REST API integration | JSON payload to endpoints |
| **generateXml** | XML file creation | Custom XML schemas |

## Configuration Examples

### Inbound Agent (Local Directory)
```javascript
{
  name: 'local_connection_batch_100',
  display_name: 'HR Local Directory',
  source: 'Local',
  type: 'Inbound',
  handler: 'sendHrData',
  mapping: 'hrData',
  queue: 'hr_csv_data',
  batch_size: 100,
  settings: {
    directory_path: '/data/hr/incoming'
  }
}
```

### Outbound Agent (API)
```javascript
{
  name: 'api_1_outbound',
  display_name: 'External Partner API',
  source: 'API',
  type: 'Outbound',
  handler: 'sendApiData',
  mapping: 'api1Outbound',
  queue: 'api_1_outbound_queue',
  batch_size: 50,
  settings: {
    api_url: 'https://api.partner.com/employees',
    api_key: 'your_api_key',
    timeout: 45000,
    retries: 3
  }
}
```

## Available Commands

### Development Commands
```bash
# Run with development monitoring
npm run dev

# Run automation script (includes cleanup)
npm run agent

# Database operations
npm run db          # Sync database
npm run db:refresh  # Refresh database
```

### Production Commands
```bash
# Outbound agents
npm run outbound_csv    # CSV generation
npm run outbound_xml    # XML generation
npm run outbound_api_1  # API outbound (key auth)
npm run outbound_api_2  # API outbound (token auth)

# Test API servers
npm run test_api_1      # Test server on port 3051
npm run test_api_2      # Test server on port 3052
```

### Direct Execution
```bash
# Run specific agent
node index.js --agent <agent_name>

# Run with automation (cleanup + execution)
node tests/agent.js --agent <agent_name>
```

## Data Mapping

### Inbound Mapping (CSV to Database)
```json
{
  "Identity.email": "email",
  "Identity.first_name": "firstName",
  "Identity.last_name": "lastName",
  "Identity.eid": "employeeId"
}
```

### Outbound Mapping (Database to API)
```json
{
  "mappingType": "apiTransform",
  "dataTransform": {
    "employee": {
      "id": "{{Identity.eid}}",
      "email": "{{Identity.email}}",
      "name": "{{Identity.first_name}} {{Identity.last_name}}"
    }
  }
}
```

## Monitoring and Performance

### Performance Metrics
- Processing time and throughput
- Success/failure rates
- API response times
- Queue depths and processing delays
- Resource utilization

### Logging
- Structured JSON logging
- Daily log rotation
- Error tracking with context
- Performance report generation

### Health Checks
- Database connectivity
- RabbitMQ status
- Agent configuration validation
- External service availability

## Security Features

### Data Protection
- Encrypted sensitive configuration
- TLS/SSL for all communications
- Secure credential management
- Audit trail logging

### Access Control
- Role-based permissions
- API key authentication
- Service account management
- Network security controls

## Support and Troubleshooting

### Common Issues
1. **Database Connection**: Check PostgreSQL status and credentials
2. **RabbitMQ Issues**: Verify queue status and connections
3. **File Processing**: Check permissions and file formats
4. **API Failures**: Review authentication and endpoint availability

### Getting Help
- Check the detailed documentation in each section
- Review log files in `/logs/` directory
- Examine performance reports in `/performances/` directory
- Verify agent configurations in the database

### Performance Tuning
- Adjust batch sizes based on system capacity
- Optimize database queries and indexes
- Configure appropriate timeout values
- Monitor memory usage and adjust limits

## Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make changes with appropriate tests
4. Submit a pull request with detailed description

### Code Standards
- Follow existing code style and patterns
- Add comprehensive error handling
- Include performance monitoring
- Update documentation for new features

## License

This project is proprietary software developed by CareMate for internal use and authorized partners.

---

For detailed information on specific topics, please refer to the individual documentation files linked above.
